spring.application.name=xhs-oa
server.servlet.context-path=/xhs-oa
server.max-http-header-size=32KB
logging.config=classpath:logback.xml
management.endpoints.enabled=true


rpc.registry.name=zookeeper

spring.datasource.sql-script-encoding=utf-8
spring.datasource.platform=mysql
spring.datasource.initialization-mode=never
#spring.datasource.schema=classpath:schema-mysql.sql
#spring.datasource.data=classpath:data-mysql.sql
spring.http.encoding.force=true
spring.http.encoding.charset=UTF-8
spring.http.encoding.enabled=true
server.tomcat.uri-encoding=UTF-8
server.tomcat.max-threads=800
spring.profiles.active=local
spring.servlet.multipart.max-file-size=700MB
spring.servlet.multipart.max-request-size=700MB

mybatis.type-aliases-package=com.xhs.oa.common.model
mybatis.mapper-locations=classpath:sqlmap/*/*.xml
#mybatis.config-location=classpath:mybatis-config.xml
qiniu.bucket_name=intdoc
qiniu.bucket_domain=https://poseidon-static.xiaohongshu.com
qiniu.bucket_expire=3600
qiniu.public.bucket=merchantenter
qiniu.public_domain=http://p0y92lejl.bkt.clouddn.com

qcloud.bucket=intdoc
qcloud.appid=10008268
qcloud.hosts=intdoc-10008268.image.myqcloud.com
qcloud.sign_expires_in=3600


spring.freemarker.template-loader-path=classpath:/static/

mock_user_pwd=123456
#italent_app_id=908
#italent_secret=4e44aab8476a4557b9fe6f0281d601c8
#italent_tenant_id=106108
#italent_grant_type=client_credentials

####  \u91CD\u65B0\u7533\u8BF7\u7684italent\u6388\u6743Sceret\uFF0C\u8BF7\u5176\u4ED6\u7CFB\u7EDF\u4E0D\u8981\u4F7F\u7528\u3002\u5982\u9700\u67E5\u8BE2\u5317\u68EE\uFF0C\u8BF7\u91CD\u65B0\u7533\u8BF7
italent_app_id=908
italent_secret=fc7a6e07804648d3a603358534f00f09
italent_tenant_id=106108
italent_grant_type=client_credentials

mail.appAlias=xhsoa
mail.appKey=xhsoa
# \u5E94\u7528\u7CFB\u7EDF\u6CE8\u518C\u8D26\u53F7
#app_alias: xhsoa
# \u5E94\u7528\u7CFB\u7EDF\u540D\u79F0 app_name: oa\u7CFB\u7EDF
#app_key: xhsoa
# \u90AE\u4EF6\u4EE3\u7406\u9009\u62E9\u7B56\u7565 0: \u9ED8\u8BA4 1: \u4F18\u9009 2: \u5F3A\u5236\u4F7F\u7528
#policy_type: 2
# \u6307\u5B9A\u4F7F\u7528\u7684\u90AE\u4EF6\u4EE3\u7406 \u53EF\u4E0D\u5199 \u5F3A\u5236\u4F7F\u7528\u65F6\u9700\u8981\u63D0\u4F9B
#mail_proxies: [oa_xhs]
#remark: "\u5907\u6CE8"
# \u6BCF\u65E5\u9650\u989D
#daily_quota: 500
# \u662F\u5426\u5F00\u542F\u6BCF\u65E5\u9650\u989D is_use_daily_quota: True
# \u603B\u989D\u5EA6 total_quota: 10000000
# \u914D\u989D\u4F7F\u7528\u6BD4\u4F8B\u62A5\u8B66 \u529F\u80FD\u6682\u672A\u5B9E\u73B0
#daily_warn_threshold: 90
#total_warn_threshold: 90
# \u9ED8\u8BA4\u90AE\u4EF6\u53D1\u9001\u4EBA
default_mail_from: "<EMAIL>"
# \u9ED8\u8BA4\u90AE\u4EF6\u56DE\u590D\u4EBA
#default_mail_reply_to: ""

apollo.bootstrap.enabled=true
apollo.bootstrap.namespaces=application
spring.main.lazy-initialization=true

## microsoft mail admin
mail.admin.clientId=bd763101-4de7-4d23-9eed-639fa41878c9
mail.admin.clientSecret=@vNPM86=:lhPtN/nCTEAPADmwFspcB75
mail.admin.tenant=aba30157-ad44-4a7e-8444-62a12bc39d9a

#logging.level.com.xhs.oa.form.mapper=debug
logging.level.com.xhs.oa.travel.infrastructure.persistence.mapper=debug
#logging.level.com.xhs.oa.supplier.mapper=debug
logging.level.com.xhs.oa.activity.infrastructure.persistence.mapper=debug
logging.level.com.xhs.oa.activity.domain.service=debug

## ctrip config
ctrip.app.key=obk_xiaohongshu
ctrip.app.security=vC5~JZF~LFkl^iZb@528csXu
ctrip.xhs.corpId=xiaohongshu
ctrip.subAccount.name=xiaohongshu_\u63D0\u524D\u5BA1\u6279
ctrip.version=1.0

##allow overriding
spring.main.allow-bean-definition-overriding=true

apm.tracing.name=zipkin

#redschedule
redschedule.appid=xhsoa
redschedule.domain=ep
