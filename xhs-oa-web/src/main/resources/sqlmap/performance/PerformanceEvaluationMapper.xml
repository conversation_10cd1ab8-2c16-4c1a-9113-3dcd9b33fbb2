<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.performance.mapper.PerformanceEvaluationMapper" > 

	<sql id="BaseColumn">
          id     id,
          ref_id     refId,
          ref_type     refType,
          work_evaluation     workEvaluation,
          work_score     workScore,
          culture_evaluation     cultureEvaluation,
          culture_score     cultureScore,
          evaluation_status     evaluationStatus,
          creator_no     creatorNo,
          creator     creator,
          create_time     createTime,
          updator_no     updatorNo,
          updator     updator,
          update_time     updateTime
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.performance.model.PerformanceEvaluation">
	    select 
	    <include refid="BaseColumn"/>
	    from performance_evaluation
	    where id = #{id}
	</select>


  <insert id="insert" parameterType="com.xhs.oa.performance.model.PerformanceEvaluation" useGeneratedKeys="true" keyProperty="id">
    insert into performance_evaluation (
          ref_id,
          ref_type,
          work_evaluation,
          work_score,
          culture_evaluation,
          culture_score,
          evaluation_status,
          creator_no,
          creator,
          create_time,
          updator_no,
          updator,
          update_time
      )
    values (
           #{refId},
           #{refType},
           #{workEvaluation},
           #{workScore},
           #{cultureEvaluation},
           #{cultureScore},
           #{evaluationStatus},
           #{creatorNo},
           #{creator},
           now(),
           #{updatorNo},
           #{updator},
           now()
      )
  </insert>

  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.performance.model.PerformanceEvaluation">
    update performance_evaluation
    set 
           id = #{id},

           ref_id = #{refId},

           ref_type = #{refType},

           work_evaluation = #{workEvaluation},

           work_score = #{workScore},

           culture_evaluation = #{cultureEvaluation},

           culture_score = #{cultureScore},

           evaluation_status = #{evaluationStatus},

           creator_no = #{creatorNo},

           creator = #{creator},

           create_time = #{createTime},

           updator_no = #{updatorNo},

           updator = #{updator},

         update_time = #{updateTime}
    where id = #{id}
  </update>

    <update id="updatePerformanceSelfEvaluationInfo" parameterType="com.xhs.oa.performance.model.PerformanceEvaluation">
        update performance_evaluation
    set
           work_evaluation = #{workEvaluation},
           work_score = #{workScore},
           updator_no = #{updatorNo},
           updator = #{updator},
          update_time = now()
    where id = #{id}
    </update>

    <update id="updateCultureEvalutionInfo" parameterType="com.xhs.oa.performance.model.PerformanceEvaluation">
         update performance_evaluation
     set
           culture_evaluation = #{cultureEvaluation},
           culture_score = #{cultureScore},
           updator_no = #{updatorNo},
           updator = #{updator},
          update_time = now()
    where id = #{id}
    </update>

    <select id="getUnSelfEvaluateCount" parameterType="com.xhs.oa.performance.param.ProgressStatisticsQueryParam" resultType="java.lang.Integer">
        select count(distinct t.user_id)
        from performance_user_task t
        <if test="departmentIds != null">
            join ACT_ID_USER u on u.ID_=t.user_id
        </if>
        left join performance_evaluation e on e.ref_id=t.id and e.ref_type='self' and e.evaluation_status=1
        <include refid="CountWhere"/>
    </select>

    <select id="getUnSurroundEvaluatedCount" parameterType="com.xhs.oa.performance.param.ProgressStatisticsQueryParam" resultType="java.lang.Integer">
        select count(distinct t.user_id)
        from performance_user_task t
        <if test="departmentIds != null">
            join ACT_ID_USER u on u.ID_=t.user_id
        </if>
        join performance_user_surround s on s.performance_user_task_id=t.id and s.is_valid=1 and s.is_join=1
        left join performance_evaluation e on e.ref_id=s.id and e.ref_type='surround' and e.evaluation_status=1
        <include refid="CountWhere"/>
    </select>

    <sql id="CountWhere">
        where t.is_valid=1 and t.performance_task_id=#{performanceTaskId}
        <if test="departmentIds != null">
            and u.DEPARTMENT_ID in
            <foreach collection="departmentIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        and e.id is null
    </sql>

    <sql id="SurroundEvaluationCountSql">
        (select count(1) from performance_evaluation  e1
            join performance_user_surround s1 on e1.ref_id=s1.id and e1.ref_type=#{surroundEvaluationType}
            where  s1.performance_user_task_id=ut.id and e1.evaluation_status=1 and s1.is_valid=1 and s1.is_join = 1)
    </sql>

    <sql id="joinUserTaskIdByEvaluationStatus">
        select
          ut.id userTaskId
        from performance_user_task ut
        join performance_user_surround s on ut.id = s.performance_user_task_id and s.is_valid = 1 and s.is_join = 1
        where
            ut.performance_task_id = #{performanceTaskId}
            group by ut.id
        having
        <if test="performanceEvalutionStatus == 'wait_evaluation'">
            <include refid="SurroundEvaluationCountSql"/> = 0
        </if>
        <if test="performanceEvalutionStatus == 'evaluationing'">
            count(1) > <include refid="SurroundEvaluationCountSql"/>
            and
            <include refid="SurroundEvaluationCountSql"/> > 0
        </if>
        <if test="performanceEvalutionStatus == 'complete_evaluation'">
            count(1) =  <include refid="SurroundEvaluationCountSql"/>
        </if>
    </sql>

    <sql id="joinUserTaskIdByPerformanceRank">
        select
           er.performance_user_task_id as rankUserTaskId
           from performance_config pc
           join performance_evaluation_result er on pc.id = er.performance_config_id
           where pc.is_valid =1 and pc.performance_task_id = #{performanceTaskId}
                and pc.confirm_status = 'finished'
    </sql>

    <sql id="performanceManageProcessWhere">
        from performance_user_task ut
        left join performance_evaluation e on ut.id = e.ref_id and e.ref_type = #{selfEvaluationType}
        join ACT_ID_USER u on ut.user_id = u.ID_
        <if test="performanceEvalutionStatus !=null and performanceEvalutionStatus!=''">
            join ( <include refid="joinUserTaskIdByEvaluationStatus"/> ) uts on ut.id = uts.userTaskId
        </if>
        <if test="performanceRank != null">
            left join ( <include refid="joinUserTaskIdByPerformanceRank"/> ) pr on ut.id = pr.rankUserTaskId
        </if>
        where
        ut.performance_task_id = #{performanceTaskId}
        and ut.is_valid = 1
        <if test="departmentIds != null and departmentIds.size>0">
            and u.DEPARTMENT_ID in(
            <foreach collection="departmentIds" separator="," item="item">
                #{item}
            </foreach>
            )
        </if>
        <if test="performanceRank != null">
            <if test="performanceRank == 'wait_rank' ">
                and pr.rankUserTaskId is null
            </if>
            <if test="performanceRank == 'complete_rank' ">
                and pr.rankUserTaskId is not null
            </if>
        </if>
        <if test="evaluateTargetUserId != null and evaluateTargetUserId != ''">
            and ut.user_id = #{evaluateTargetUserId}
        </if>
        <if test = "selfEvalutionStatus != null and selfEvalutionStatus !=''">
            <if test = "selfEvalutionStatus == 'complete_self_evaluation'">
                and e.evaluation_status = 1
            </if>
            <if test = "selfEvalutionStatus == 'wait_self_evaluation'">
                and (e.evaluation_status = 0 or e.evaluation_status is null)
            </if>
        </if>
    </sql>

    <select id="findPerformanceManageProcessPage" parameterType="com.xhs.oa.performance.param.ProcessManageQueryParam" resultType="com.xhs.oa.performance.dto.ProcessManageDTO">
        select
        ut.user_id              userId,
        u.FIRST_                userName,
        e.evaluation_status     evaluationStatus,
        ut.id                   userTaskId,
        u.DEPARTMENT_ID         departmentId
        <if test="performanceRank != null">
            ,pr.rankUserTaskId
        </if>
        <include refid="performanceManageProcessWhere"/>
        order by ut.user_id
        limit #{start},#{pageSize}
    </select>

    <select id="getCountPerformanceManageProcess" parameterType="com.xhs.oa.performance.param.ProcessManageQueryParam" resultType="java.lang.Integer">
        select count(1)
        <include refid="performanceManageProcessWhere"/>
    </select>

    <sql id="findMySelfEvaluationWhere">
        from performance_task pt
        join performance_user_task ut on pt.id = ut.performance_task_id
        where pt.is_valid = 1
              <![CDATA[  and pt.self_evaluation_time <= now() ]]>
              and ut.is_valid = 1
              and ut.user_id = #{userId}
        order by pt.id desc
    </sql>

    <select id="findPageMySelfEvaluationPerformance" resultType="com.xhs.oa.performance.dto.SelfEvaluationStatusDTO" parameterType="com.xhs.oa.performance.param.SelfEvaluationQueryParam">
        select
            pt.id                       performanceTaskId,
            pt.performance_type         performanceType,
            ut.id                       performanceUserTaskId,
            pt.performance_name         performanceName,
            pt.end_time                 endTime,
            pt.is_published             isPublished,
            ut.is_published             promotionUserIsPublished
        <include refid="findMySelfEvaluationWhere"/>
        limit #{start},#{pageSize}
    </select>

    <select id="findPageCountMySelfEvaluation" parameterType="com.xhs.oa.performance.param.SelfEvaluationQueryParam" resultType="java.lang.Integer">
        select count(1)
        <include refid="findMySelfEvaluationWhere"/>
    </select>

    <select id="findEvalutionInfoByRefIdAndType" resultType="com.xhs.oa.performance.model.PerformanceEvaluation">
        select
        <include refid="BaseColumn"/>
        from performance_evaluation
        where ref_type = #{refType}
              and ref_id = #{refId}
    </select>

    <delete id="deleteEvaluationInfoByPrimaryId">
        delete from performance_evaluation
        where  id = #{id}
    </delete>

    <sql id="SelfEvaluation">
        e.work_score     workScore,
        e.culture_score     cultureScore,
        e.ref_id  refId
    </sql>

    <select id="getSelfEvaluation" resultType="com.xhs.oa.performance.model.PerformanceEvaluation">
        select <include refid="SelfEvaluation"/>
        from performance_user_task t
        join performance_evaluation e on e.ref_id=t.id and e.ref_type='self' and e.evaluation_status=1
        where t.is_valid=1 and t.performance_task_id=#{performanceTaskId} and t.user_id=#{userId}
    </select>

    <select id="getSelfEvaluationByUserTaskId" resultType="com.xhs.oa.performance.model.PerformanceEvaluation">
        select
        <include refid="BaseColumn"/>
        from performance_evaluation
        where  ref_type = #{selfEvaution}
               and ref_id = #{userTaskId}
    </select>

    <update id="updateSubmitPerformanceEvaluation">
        update performance_evaluation
        set evaluation_status = 1,
            updator = #{userName},
            updator_no = #{userId},
            update_time = now()
        where id = #{evaluationId}
    </update>

    <update id="updateToDraftPerformanceEvaluation">
        update performance_evaluation
        set evaluation_status = 0,
        updator = #{userName},
        updator_no = #{userId},
        update_time = now()
        where id = #{evaluationId}
    </update>

    <select id="getUnEvaluationUserIdList" resultType="java.lang.String">
        select a.user_id from
        performance_user_task a left join
        performance_evaluation b  on a.id = b.ref_id and b.ref_type = #{refType}
        where a.is_valid = 1 and a.performance_task_id = #{taskId} and a.user_id in
        <foreach collection="userIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and (b.evaluation_status is null || b.evaluation_status = 0)
    </select>

    <select id="getUnEvaluationUserIdListByDeparmtentList" resultType="java.lang.String">
        select a.user_id
        from performance_user_task a join ACT_ID_USER b on a.user_id  = b.ID_
        <if test="departmentIdList != null">
		join department c on b.DEPARTMENT_ID = c.department_id
        and c.department_id in
		<foreach collection="departmentIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        </if>
        left join performance_evaluation d  on a.id = d.ref_id and d.ref_type = #{refType}
        where a.is_valid = 1 and  a.performance_task_id = #{taskId}
        and (d.evaluation_status is null || d.evaluation_status = 0)

    </select>

    <select id="getAllEvaluationInfoByDepartmentIDList" resultType="com.xhs.oa.performance.dto.PerformanceGenResultDTO">
        select
        b.performance_task_id taskId,
        b.id userTaskId,
        b.user_id userId,
        c.evaluation_role_type evaluationRoleType,
        d.culture_score cultureEvaluationScore,
        d.work_score workEvaluationScore,
        d.evaluation_status evaluationStatus,
        d.id evaluationId,
        d.work_evaluation workEvaluationContent,
        d.culture_evaluation cultureContent
        from
        performance_user_task b
        join ACT_ID_USER u on b.user_id = u.ID_
        <if test="deptIds != null" >
            and u.DEPARTMENT_ID in
            <foreach collection="deptIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        join performance_user_surround c on b.id = c.performance_user_task_id and c.is_join = 1 and c.is_valid = 1
        left join performance_evaluation d on c.id = d.ref_id and d.ref_type = #{refType}
        where b.is_valid = 1 and b.performance_task_id  = #{taskId}
    </select>

    <select id="getAllCaptainEvaluationInfo" resultType="com.xhs.oa.performance.dto.PerformanceGenResultDTO">
        select
        b.performance_task_id taskId,
        b.id userTaskId,
        b.user_id userId,
        c.evaluation_role_type evaluationRoleType,
        d.culture_score cultureEvaluationScore,
        d.work_score workEvaluationScore,
        d.evaluation_status  evaluationStatus,
        d.id evaluationId,
        d.work_evaluation workEvaluationContent,
        d.culture_evaluation cultureContent
        from
        performance_user_task b
        join performance_user_surround c on b.id = c.performance_user_task_id and c.is_join = 1 and c.is_valid = 1
        left join performance_evaluation d on c.id = d.ref_id and d.ref_type = #{refType}
        where b.performance_task_id = #{taskId} and b.is_valid = 1  and b.user_id in
        <foreach collection="userIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>


    <select id="getUnCompleteSourroundUserIdList" resultType="java.lang.String">
        select
        distinct  b.evaluation_user_id
        from
        performance_user_task a
        join performance_user_surround b on a.id = b.performance_user_task_id and b.is_join = 1 and b.is_valid = 1
        left join performance_evaluation d on b.id = d.ref_id and d.ref_type = #{refType}
        where a.is_valid = 1 and a.performance_task_id  = #{taskId} and  a.user_id in
        <foreach collection="userIdList" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        and (d.evaluation_status is null || d.evaluation_status = 0)
    </select>

    <select id="getUnCompleteSourroundUserListByDepartList" resultType="java.lang.String">
        select
        distinct  b.evaluation_user_id
        from
        performance_user_task a
        <if test="departmentIdList != null">
            join ACT_ID_USER u on a.user_id = u.id_
            join department p on u.department_id = p.department_id
            and p.department_id in
            <foreach collection="departmentIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        join performance_user_surround b on a.id = b.performance_user_task_id and b.is_join = 1 and b.is_valid = 1
        left join performance_evaluation d on b.id = d.ref_id and d.ref_type = #{refType}
        where a.is_valid = 1 and a.performance_task_id  = #{taskId}
        and (d.evaluation_status  is null || d.evaluation_status = 0)
    </select>

    <select id="getAllSurroundWorkResult" resultType="com.xhs.oa.performance.dto.SurroundWorkEvaluationDto">
        select
               us.id                          surroundId,
               us.performance_user_task_id userTaskId,
               us.evaluation_user_id evaluateUserId,
               e.work_score workMark,
               e.work_evaluation workRemark,
               e.evaluation_status      submitEvaluationStatus
        from performance_user_surround us
        left join performance_evaluation e on e.ref_id=us.id and e.ref_type=#{refType}
        where us.is_valid=1 and us.is_join=1 and us.performance_user_task_id in
        <foreach collection="userTaskIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="findPerformanceSelfEvaluationListByTaskId" resultType="com.xhs.oa.performance.dto.SelfEvaluationDTO">
        select
        ut.user_id     userId,
        ut.id          userTaskId,
        pe.evaluation_status   evaluationStatus,
        pe.work_score       workScore,
        pe.work_evaluation    workEvaluation,
        pe.culture_evaluation   cultureEvaluation,
        pe.id                   evaluationId
        from performance_user_task ut
        left join performance_evaluation pe on ut.id = pe.ref_id and pe.ref_type = #{refType}
        where ut.performance_task_id = #{performanceTaskId} and ut.is_valid = 1
        order by ut.user_id
        limit #{pageQuery.start},#{pageQuery.pageSize}
    </select>

    <update id="updateEvaluationStatusWithNoEvauation">
        update performance_evaluation
        set evaluation_status = 0,
            updator_no = #{userId},
            updator = #{userName},
            update_time = now()
        where ref_type = #{refType}
        and ref_id = #{refId}
        and evaluation_status = 1
    </update>

    <update id="updatePromotionEvaluationInfo">
        update performance_evaluation
        set work_score=#{workScore},
          work_evaluation=#{workEvaluation},
          evaluation_status=#{evaluationStatus},
          updator=#{updator},
          updator_no=#{updatorNo}
        where id=#{id}
    </update>

    <select id="findPromotionSurroundEvaluateList" parameterType="java.lang.Long" resultType="com.xhs.oa.performance.dto.PromotionSurroundEvaluateDTO">
        select us.performance_user_task_id      performanceUserTaskId,
        us.evaluation_status                    evaluationStatus,
        us.evaluation_role_type                   evaluationRoleType,
        e.work_score                              workScore,
        e.work_evaluation                         workEvaluation,
        us.evaluation_user_id                   evaluateUserId,
        us.id                                   surroundId
        from performance_user_surround us
        join performance_evaluation e on e.ref_id=us.id and e.ref_type='surround'
        where us.is_valid=1 and us.is_join=1
        and us.performance_user_task_id = #{performanceUserTaskId}
        and e.evaluation_status = 1
        order by us.id asc
    </select>

    <select id="findPromotionSurroundWithEvaluateDataList" resultType="com.xhs.oa.performance.dto.PromotionSurroundEvaluateDTO">
        select us.performance_user_task_id      performanceUserTaskId,
        us.evaluation_status                    evaluationStatus,
        us.evaluation_role_type                 evaluationRoleType,
        e.work_score                              workScore,
        e.work_evaluation                         workEvaluation,
        e.evaluation_status                     detailEvaluationStatus,
        us.evaluation_user_id                   evaluateUserId,
        us.id                                   surroundId
        from performance_user_surround us
        left join performance_evaluation e on e.ref_id=us.id and e.ref_type='surround'
        where us.is_valid=1 and us.is_join=1
        and us.performance_user_task_id = #{performanceUserTaskId}
        order by us.id asc
    </select>

    <select id="findSelfEvaluateStatusListByPerformanceUserTaskIds" resultType="com.xhs.oa.performance.dto.SelfEvaluationStatusDTO">
        select
            ut.id                       performanceUserTaskId,
            pe.evaluation_status        evaluationStatus
        from performance_user_task ut
        left join performance_evaluation pe on ut.id = pe.ref_id and pe.ref_type = #{selfEvaluationType}
        where ut.is_valid = 1
        and ut.id in (
          <foreach collection="performanceUserTaskIds" item="item" separator=",">
              #{item}
          </foreach>
        )

    </select>

    <select id="getJudgeEvaluationResult" resultType="com.xhs.oa.performance.dto.PromotionSurroundEvaluateDTO">
        select
          pus.performance_user_task_id performanceUserTaskId,
          pus.id surroundId,
          pus.evaluation_user_id evaluateUserId,
          pus.evaluation_role_type evaluationRoleType,
          pus.evaluation_status evaluationStatus,
          pe.id evaluationId,
          pe.evaluation_status isSubmit,
          pe.work_score workScore,
          pe.work_evaluation workEvaluation
        from performance_user_surround pus
        left join performance_evaluation pe on pe.ref_id=pus.id and pe.ref_type=#{evaluationType}
        where pus.is_join=1 and pus.is_valid=1 and pus.id=#{surroundId}
    </select>

    <update id="updateEvaluateContentBySurroundId" >
        update performance_evaluation
        set work_evaluation = #{evaluateContent},
        updator_no = #{userId},
        updator = #{userName},
        update_time  = now()
        where ref_id = #{refId} and ref_type = #{refType}
    </update>
</mapper>
