<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.BidSourceChangeMapper" > 

	<sql id="BaseColumn">
          id     id,
          source_id     sourceId,
          source_type_before     sourceTypeBefore,
          source_type_after     sourceTypeAfter,
          attachment_name     attachmentName,
          attachment_url     attachmentUrl,
          source_change_reason     sourceChangeReason,
          operator_no     operatorNo,
          operator     operator,
          create_time     createTime
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.purchasebid.model.BidSourceChange">
	    select 
	    <include refid="BaseColumn"/>
	    from bid_source_change
	    where id = #{id}
	</select>

    <select id="selectBySourceId" resultType="com.xhs.oa.purchasebid.model.BidSourceChange">
        select <include refid="BaseColumn"/>
        from bid_source_change
        where source_id=#{sourceId}
        order by id desc
    </select>


    <insert id="insert" parameterType="com.xhs.oa.purchasebid.model.BidSourceChange">
    insert into bid_source_change (
          source_id,
          source_type_before,
          source_type_after,
          attachment_name,
          attachment_url,
          source_change_reason,
          operator_no,
          operator,
          create_time
      )
    values (
           #{sourceId},
           #{sourceTypeBefore},
           #{sourceTypeAfter},
           #{attachmentName},
           #{attachmentUrl},
           #{sourceChangeReason},
           #{operatorNo},
           #{operator},
           now()
      )
  </insert>


    <delete id="deleteChangeInfobySourceId">
        delete from bid_source_change
        where source_id = #{sourceId}
    </delete>

  
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidSourceChange">
    update bid_source_change
    set 
           id = #{id},

           source_id = #{sourceId},

           source_type_before = #{sourceTypeBefore},

           source_type_after = #{sourceTypeAfter},

           attachment_name = #{attachmentName},

           attachment_url = #{attachmentUrl},

           source_change_reason = #{sourceChangeReason},

           operator_no = #{operatorNo},

           operator = #{operator},

         create_time = #{createTime}
    where id = #{id}
  </update>

</mapper>   
