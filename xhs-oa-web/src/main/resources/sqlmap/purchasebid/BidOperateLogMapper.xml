<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.BidOperateLogMapper" > 

	<sql id="BaseColumn">
          id     id,
          form_num     formNum,
          operate     operate,
          param     param,
          result     result,
          result_type   resultType,
          creator_no     creator<PERSON>o,
          creator     creator,
          create_time     createTime
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.purchasebid.model.BidOperateLog">
	    select 
	    <include refid="BaseColumn"/>
	    from bid_operate_log
	    where id = #{id}
	</select>


  <insert id="insert" parameterType="com.xhs.oa.purchasebid.model.BidOperateLog">
    insert into bid_operate_log (
          form_num,
          operate,
          param,
          result,
          result_type,
          creator_no,
          creator,
          create_time
      )
    values (
           #{formNum},
           #{operate},
           #{param},
           #{result},
           #{resultType},
           #{creatorNo},
           #{creator},
           now()
      )
  </insert>

  
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidOperateLog">
    update bid_operate_log
    set 
           id = #{id},

           form_num = #{formNum},

           operate = #{operate},

           param = #{param},

           result = #{result},

           creator_no = #{creatorNo},

           creator = #{creator},

         create_time = #{createTime}
    where id = #{id}
  </update>

</mapper>   
