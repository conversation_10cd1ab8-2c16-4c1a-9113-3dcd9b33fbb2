<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.BidSupplierAuditMapper" > 

	<sql id="BaseColumn">
          id     id,
          supplier_id     supplierId,
          audit_type     auditType,
          audit_result     auditResult,
          attachment_name     attachmentName,
          attachment_url     attachmentUrl,
          audit_comment auditComment,
          operator_no     operatorNo,
          operator     operator,
          create_time     createTime
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.purchasebid.model.BidSupplierAudit">
	    select 
	    <include refid="BaseColumn"/>
	    from bid_supplier_audit
	    where id = #{id}
	</select>

    <select id="findAuditResultByAuditTypeAndSupplierIds" resultType="com.xhs.oa.purchasebid.model.BidSupplierAudit">
        select
          supplier_id supplierId,
          audit_result auditResult
        from bid_supplier_audit
        where audit_type=#{auditType} and supplier_id in
        <foreach collection="supplierIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectBySupplierId" resultType="com.xhs.oa.purchasebid.model.BidSupplierAudit">
        select <include refid="BaseColumn"/>
        from bid_supplier_audit
        where supplier_id=#{supplierId}
        order by id desc
    </select>

    <select id="findSupplierAuditResults" resultType="com.xhs.oa.purchasebid.dto.SupplierAuditResultDto">
        select bsa.supplier_id, bsa.audit_result from
        (
        select max(id) id
        from bid_supplier_audit
        where bsa.auditType=#{auditType} and supplier_id in
        <foreach collection="supplierIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        group by supplier_id
        ) temp join bid_supplier_audit bsa on temp.id=bsa.id
    </select>


    <insert id="insert" parameterType="com.xhs.oa.purchasebid.model.BidSupplierAudit">
    insert into bid_supplier_audit (
          supplier_id,
          audit_type,
          audit_result,
          attachment_name,
          attachment_url,
          audit_comment,
          operator_no,
          operator,
          create_time
      )
    values (
           #{supplierId},
           #{auditType},
           #{auditResult},
           #{attachmentName},
           #{attachmentUrl},
           #{auditComment},
           #{operatorNo},
           #{operator},
           now()
      )
  </insert>

  
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidSupplierAudit">
    update bid_supplier_audit
    set 
           id = #{id},

           supplier_id = #{supplierId},

           audit_type = #{auditType},

           audit_result = #{auditResult},

           attachment_name = #{attachmentName},

           attachment_url = #{attachmentUrl},

           audit_comment = #{auditComment},

           operator_no = #{operatorNo},

           operator = #{operator},

         create_time = #{createTime}
    where id = #{id}
  </update>

    <update id="updateSupplierIds">
        update bid_supplier_audit
        set supplier_id=#{targetId}, flag=1
        where supplier_id=#{originId} and flag=0
    </update>

    <delete id="deleteBySupplyIds" parameterType="java.lang.Long">
        delete
        from bid_supplier_audit
        where supplier_id in (
        <foreach collection="supplierIds" item="item" separator=",">
            #{item}
        </foreach>
            )
    </delete>
</mapper>   
