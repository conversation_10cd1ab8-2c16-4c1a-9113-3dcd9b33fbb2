<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhs.oa.purchasebid.mapper.BidContractCategoryMapper">
  <resultMap id="BaseResultMap" type="com.xhs.oa.purchasebid.model.BidContractCategory">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="contract_form_num" jdbcType="VARCHAR" property="contractFormNum" />
    <result column="category_id" jdbcType="VARCHAR" property="categoryId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="creator_no" jdbcType="VARCHAR" property="creatorNo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    id, contract_form_num, category_id, creator, creator_no, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    select 
    <include refid="Base_Column_List" />
    from bid_contract_category
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    delete from bid_contract_category
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.xhs.oa.purchasebid.model.BidContractCategory" useGeneratedKeys="true">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    insert into bid_contract_category (contract_form_num, category_id, creator, 
      creator_no, create_time, update_time
      )
    values (#{contractFormNum,jdbcType=VARCHAR}, #{categoryId,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{creatorNo,jdbcType=VARCHAR},now(), now()
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.xhs.oa.purchasebid.model.BidContractCategory" useGeneratedKeys="true">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    insert into bid_contract_category
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="contractFormNum != null">
        contract_form_num,
      </if>
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="creatorNo != null">
        creator_no,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="contractFormNum != null">
        #{contractFormNum,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="creatorNo != null">
        #{creatorNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhs.oa.purchasebid.model.BidContractCategory">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    update bid_contract_category
    <set>
      <if test="contractFormNum != null">
        contract_form_num = #{contractFormNum,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="creatorNo != null">
        creator_no = #{creatorNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidContractCategory">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    update bid_contract_category
    set contract_form_num = #{contractFormNum,jdbcType=VARCHAR},
      category_id = #{categoryId,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      creator_no = #{creatorNo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <delete id="deleteByContractFormNum" >
    <!-- @mbg.generated: generated automatically, do not modify! -->
    delete from bid_contract_category
    where contract_form_num = #{contractFormNum}
  </delete>

  <delete id="deleteAll">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    delete from bid_contract_category
  </delete>

  <select id="selectContractCategory"  resultMap="BaseResultMap">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    select
    <include refid="Base_Column_List" />
    from bid_contract_category
    where contract_form_num = #{contractFormNum}
  </select>
</mapper>