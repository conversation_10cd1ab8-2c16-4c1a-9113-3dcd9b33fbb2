<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.BidRatingJudgeMapper" > 

	<sql id="BaseColumn">
          id     id,
          bidding_id     biddingId,
          judge_type     judgeType,
          judge_id     judgeId,
          is_entrusted isentrusted,
          join_type     joinType,
          is_valid     isValid,
          create_time     createTime,
          creator_no     creatorNo,
          creator     creator,
          update_time     updateTime,
          updator_no     updatorNo,
          updator     updator
    </sql>

    <update id="invalidateRecords">
        update bid_rating_judge
        set is_valid=0
        where is_valid=1 and id in
        <foreach collection="list" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.purchasebid.model.BidRatingJudge">
	    select 
	    <include refid="BaseColumn"/>
	    from bid_rating_judge
	    where id = #{id}
	</select>

    <select id="findBiddingJudges" resultType="com.xhs.oa.purchasebid.model.BidRatingJudge">
        select
          <include refid="BaseColumn"/>
        from bid_rating_judge
        where is_valid=1 and bidding_id=#{biddingId} and join_type in
        <foreach collection="joinTypes" item="type" separator="," open="(" close=")">
            #{type}
        </foreach>
    </select>

    <select id="findJudgeByUserId" resultType="com.xhs.oa.purchasebid.model.BidRatingJudge">
        select
          <include refid="BaseColumn"/>
        from bid_rating_judge
        where is_valid=1 and bidding_id=#{biddingId} and judge_id=#{userId} and join_type!=#{joinType}
    </select>

    <select id="findAllBiddingJudges" resultType="com.xhs.oa.purchasebid.model.BidRatingJudge">
        select <include refid="BaseColumn"/>
        from bid_rating_judge
        where is_valid=1 and bidding_id=#{biddingId}
    </select>

    <select id="findBiddingJudgesByBidIdAndJoinType" resultType="com.xhs.oa.purchasebid.model.BidRatingJudge">
        select
        <include refid="BaseColumn"/>
        from bid_rating_judge
        where is_valid=1 and bidding_id=#{biddingId}
        <if test="joinType != null and joinType != ''">
            and join_type =#{joinType}
        </if>
        <if test="userId !=null and userId != '' ">
            and judge_id = #{userId}
        </if>
    </select>

    <select id="findByJudgeTypeAndJoinType" resultType="com.xhs.oa.purchasebid.model.BidRatingJudge">
        select <include refid="BaseColumn"/>
        from bid_rating_judge
        where is_valid=1 and bidding_id=#{biddingId} and judge_type=#{judgeType} and join_type=#{joinType}
    </select>

    <select id="findBiddingJudgesByJudgeType" resultType="com.xhs.oa.purchasebid.model.BidRatingJudge">
        select
        <include refid="BaseColumn"/>
        from bid_rating_judge
        where is_valid=1 and bidding_id=#{biddingId}
        and judge_type in
        <foreach collection="judgeTypes" item="judgeType" separator="," open="(" close=")">
            #{judgeType}
        </foreach>
        and join_type in
        <foreach collection="joinTypes" item="type" separator="," open="(" close=")">
            #{type}
        </foreach>
    </select>


    <update id="batchUpdateJudgeStatus" >
        update bid_rating_judge
        set join_type = #{joinType},
        updator_no = #{updatorNo},
        updator = #{updator},
        update_time = now()
        where id in
        <foreach collection="idList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <update id="updateIsEntrusted">
        update bid_rating_judge
        set is_entrusted=1, updator_no = #{updatorNo}, updator = #{updator}, update_time = now()
        where is_entrusted=0 and id=#{id}
    </update>

    <update id="updateJudge">
        update bid_rating_judge
        set judge_id=#{paramsMap.newJudgeId}, updator_no = #{paramsMap.updatorNo}, updator = #{paramsMap.updator}, update_time = now()
        where id=#{paramsMap.id}
    </update>

    <insert id="insert" parameterType="com.xhs.oa.purchasebid.model.BidRatingJudge" keyProperty="id" useGeneratedKeys="true">
    insert into bid_rating_judge (
          bidding_id,
          judge_type,
          judge_id,
          is_entrusted,
          join_type,
          is_valid,
          create_time,
          creator_no,
          creator,
          update_time,
          updator_no,
          updator
      )
    values (
           #{biddingId},
           #{judgeType},
           #{judgeId},
           #{isEntrusted},
           #{joinType},
           1,
           now(),
           #{creatorNo},
           #{creator},
           now(),
           #{updatorNo},
          #{updator}
      )
  </insert>

    <insert id="batchInsert">
        insert into bid_rating_judge (
          bidding_id,
          judge_type,
          judge_id,
          is_entrusted,
          join_type,
          is_valid,
          create_time,
          creator_no,
          creator,
          update_time,
          updator_no,
          updator
      )
    values
    <foreach collection="list" item="item" separator=",">
        (
        #{item.biddingId},
        #{item.judgeType},
        #{item.judgeId},
        #{item.isEntrusted},
        #{item.joinType},
        1,
        now(),
        #{item.creatorNo},
        #{item.creator},
        now(),
        #{item.updatorNo},
        #{item.updator}
        )
    </foreach>
    </insert>

</mapper>   
