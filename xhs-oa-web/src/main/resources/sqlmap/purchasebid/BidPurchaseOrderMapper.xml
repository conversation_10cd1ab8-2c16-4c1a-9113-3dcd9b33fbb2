<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.BidPurchaseOrderMapper" > 

	<sql id="BaseColumn">
          id     id,
          form_num     formNum,
          order_form_type     orderFormType,
          contract_form_num     contractFormNum,
          special_payment_form_num     specialPaymentFormNum,
          supplier_name     supplierName,
          payment_method  paymentMethod,
          currency     currency,
          total_origin_amount  totalOriginAmount,
          total_act_origin_amount  totalActOriginAmount,
          total_amount     totalAmount,
          already_paid     alreadyPaid,
          invoice_difference     invoiceDifference,
          current_step     currentStep,
          part_goods     partGoods,
          is_confirm_delivery  isConfirmDelivery,
          is_apply_pay  isApplyPay,
          is_terminate     isTerminate,
          is_valid     isValid,
          sent_invoice_notice  sentInvoiceNotice,
          creator_no     creator<PERSON>o,
          creator     creator,
          create_time     createTime,
          updator_no     updatorNo,
          updator     updator,
          update_time     updateTime
    </sql>

    <sql id="BpoBaseColumn">
          bpo.id     id,
          bpo.form_num     formNum,
          bpo.order_form_type     orderFormType,
          bpo.contract_form_num     contractFormNum,
          bpo.special_payment_form_num     specialPaymentFormNum,
          bpo.supplier_name     supplierName,
          bpo.payment_method  paymentMethod,
          bpo.currency     currency,
          bpo.total_origin_amount  totalOriginAmount,
          bpo.total_act_origin_amount  totalActOriginAmount,
          bpo.total_amount     totalAmount,
          bpo.already_paid     alreadyPaid,
          bpo.invoice_difference     invoiceDifference,
          bpo.current_step     currentStep,
          bpo.part_goods     partGoods,
          bpo.is_confirm_delivery  isConfirmDelivery,
          bpo.is_apply_pay  isApplyPay,
          bpo.is_terminate     isTerminate,
          bpo.is_valid     isValid,
          bpo.creator_no     creatorNo,
          bpo.creator     creator,
          bpo.create_time     createTime,
          bpo.updator_no     updatorNo,
          bpo.updator     updator,
          bpo.update_time     updateTime
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.purchasebid.model.BidPurchaseOrder">
	    select 
	    <include refid="BaseColumn"/>
	    from bid_purchase_order
	    where id = #{id}
	</select>

    <select id="findTotalOrderAmountByContractFormNum"
            resultType="com.xhs.oa.purchasebid.dto.ContractTotalRelateAmountDto">
        select
          contract_form_num relateFormNum,
          sum(total_amount) totalRelateAmount
        from bid_purchase_order
        where is_valid=1 and is_terminate=0 and current_step not in
        <foreach collection="steps" item="step" separator="," open="(" close=")">
            #{step}
        </foreach>
        and contract_form_num in
        <foreach collection="contractFormNums" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        group by contract_form_num
    </select>

    <select id="selectByFormNum" resultType="com.xhs.oa.purchasebid.model.BidPurchaseOrder">
        select <include refid="BaseColumn"/>
        from bid_purchase_order
        where is_valid=1 and form_num=#{formNum}
    </select>

    <select id="selectByFormNums" resultType="com.xhs.oa.purchasebid.model.BidPurchaseOrder">
        select
          <include refid="BaseColumn"/>
        from bid_purchase_order
        where form_num in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <sql id="PageQueryColumn">
            bpo.id  orderId,
            bpo.form_num  formNum,
            cf.audit_status  formAuditStatus,
            bpo.current_step  currentStep,
            cf.current_audit_user  currentAuditor,
            bpo.part_goods  goods,
            bpo.supplier_name  supplierName,
            bpo.payment_method  paymentMethod,
            bpo.currency  currency,
            bpo.total_origin_amount  totalAmount,
            bpo.total_act_origin_amount  totalActOriginAmount,
            bpo.already_paid  alreadyPaidAmount,
            bpo.invoice_difference  invoiceDifference,
            bpo.is_confirm_delivery  isConfirmDelivery,
            cf.creator_no  creatorNo,
            cf.be_entrusted_id  beEntrustedId
    </sql>

    <select id="queryPurchaseOrderPage" resultType="com.xhs.oa.purchasebid.dto.PurchaseOrderPageDto">
        select
        <include refid="PageQueryColumn"/>
        <include refid="PageQuery"/>

        union

        select
        <include refid="PageQueryColumn"/>
        <include refid="FormShareList"/>

        order by orderId desc
        limit #{start}, #{pageSize}
    </select>

    <sql id="FormShareList">
        from t_form_share_auth fsa
        join common_form cf on fsa.form_num = cf.form_num and cf.is_valid=1
        join bid_purchase_order bpo on bpo.form_num=fsa.form_num and bpo.is_valid=1
        where fsa.is_valid=1
        and ( (fsa.auth_value=#{shareUserId} and fsa.auth_type='personal')
            <if test="shareDeptIds!=null and shareDeptIds.size()>0">
                or (fsa.auth_value in
                <foreach collection="shareDeptIds" item="shareDeptId" separator="," open="(" close=")">
                    #{shareDeptId}
                </foreach>
                and fsa.auth_type='department')
            </if>
            )

            <if test="formNum != null and formNum != '' ">
                and bpo.form_num like concat('%',#{formNum},'%')
            </if>

            <if test="formAuditStatus != null and formAuditStatus != '' ">
                and  cf.audit_status = #{formAuditStatus}
            </if>

            <if test="beginAmount != null and endAmount != null">
                <![CDATA[ and bpo.total_amount >= #{beginAmount} and bpo.total_amount <= #{endAmount} ]]>
            </if>

            <if test="supplierName != null and supplierName != '' ">
                and bpo.supplier_name like concat('%',#{supplierName},'%')
            </if>
    </sql>

    <select id="queryPurchaseOrderCount" resultType="java.lang.Integer">
        select count(1)
        from (
            select cf.form_num
            <include refid="PageQuery"/>
            union
            select cf.form_num
            <include refid="FormShareList"/>
        ) temp
    </select>

    <sql id="OrderExportColumn">
        bpo.id orderId,
        bpo.form_num  formNum,
        cf.audit_status  formAuditStatus,
        bpo.current_step  currentStep,
        cf.current_audit_user  currentAuditor,
        bpo.part_goods  goods,
        bpo.supplier_name  supplierName,
        bpo.currency  currency,
        bpo.total_origin_amount  totalAmount,
        bpo.total_act_origin_amount  totalActOriginAmount,
        bpo.already_paid  alreadyPaidAmount,
        bpo.invoice_difference  invoiceDifference
    </sql>

    <select id="queryPurchaseOrderForExport" resultType="com.xhs.oa.purchasebid.dto.PurchaseOrderPageDto">
        select
        <include refid="OrderExportColumn"/>
        <include refid="PageQuery"/>

        union

        select
        <include refid="OrderExportColumn"/>
        <include refid="FormShareList"/>

        order by orderId desc
    </select>

    <sql id="UploadPageColumn">
        bpo.id  id,
        bpo.form_num  formNum,
        bpo.supplier_name  supplierName,
        bpo.payment_method  paymentMethod,
        bpo.total_origin_amount  totalOriginAmount,
        bpo.total_act_origin_amount  totalActOriginAmount,
        bpo.invoice_difference  invoiceDifference,
        bpo.already_paid  alreadyPaid
    </sql>

    <select id="queryWaitUploadInvoicePage" resultType="com.xhs.oa.purchasebid.model.BidPurchaseOrder">
        select
        <include refid="UploadPageColumn"/>
        <include refid="InvoicePageQuery"/>

        union

        select
        <include refid="UploadPageColumn"/>
        <include refid="UploadInvoiceFormShareList"/>

        order by id desc
        limit #{start}, #{pageSize}
    </select>

    <sql id="UploadInvoiceFormShareList">
        from t_form_share_auth fsa
        join common_form cf on fsa.form_num = cf.form_num and cf.is_valid=1
        join bid_purchase_order bpo on fsa.form_num=bpo.form_num and bpo.is_valid=1 and bpo.invoice_difference > 0
        where fsa.is_valid=1
            and ( (fsa.auth_value=#{shareUserId} and fsa.auth_type='personal')
            <if test="shareDeptIds!=null and shareDeptIds.size()>0">
                or (fsa.auth_value in
                <foreach collection="shareDeptIds" item="shareDeptId" separator="," open="(" close=")">
                    #{shareDeptId}
                </foreach>
                and fsa.auth_type='department')
            </if>
            )
            <if test="formNum != null and formNum != '' ">
                and bpo.form_num like concat('%',#{formNum},'%')
            </if>

            <if test="supplierName != null and supplierName != '' ">
                and bpo.supplier_name like concat('%',#{supplierName},'%')
            </if>

            <if test="beginAmount != null and endAmount != null">
                <![CDATA[ and bpo.total_origin_amount >= #{beginAmount} and bpo.total_origin_amount <= #{endAmount} ]]>
            </if>

            <if test="payType != null and payType != '' ">
                and bpo.payment_method = #{payType}
            </if>
    </sql>

    <select id="queryWaitUploadInvoicePageCount" resultType="java.lang.Integer">
        select count(1)
        from (
            select cf.form_num
            <include refid="InvoicePageQuery"/>
            union
            select cf.form_num
            <include refid="UploadInvoiceFormShareList"/>
        ) temp
    </select>

    <sql id="UploadInvoiceExportColumn">
        bpo.form_num  formNum,
        bpo.supplier_name  supplierName,
        bpo.payment_method  paymentMethod,
        bpo.total_origin_amount  totalOriginAmount,
        bpo.total_act_origin_amount  totalActOriginAmount,
        bpo.invoice_difference  invoiceDifference,
        bpo.already_paid  alreadyPaid
    </sql>
    <select id="queryWaitUploadInvoicePageForExport" resultType="com.xhs.oa.purchasebid.model.BidPurchaseOrder">
        select
        <include refid="UploadInvoiceExportColumn"/>
        <include refid="InvoicePageQuery"/>

        select
        <include refid="UploadInvoiceExportColumn"/>
        <include refid="UploadInvoiceFormShareList"/>

        order by id desc
    </select>

    <select id="specialPayFormCount" resultType="java.lang.Integer">
        select count(1)
        from bid_purchase_order
        where is_valid=1 and is_terminate=0 and current_step not in
        <foreach collection="steps" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and special_payment_form_num=#{specialPayFormNum}
    </select>

    <select id="contractFormCount" resultType="java.lang.Integer">
        select count(1)
        from bid_purchase_order
        where is_valid=1 and is_terminate=0 and current_step not in
        <foreach collection="steps" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and contract_form_num in
        <foreach collection="formNums" item="formNum" separator="," open="(" close=")">
            #{formNum}
        </foreach>
    </select>

    <select id="contractOrderTotalAmount" resultType="java.math.BigDecimal">
        select sum(total_amount)
        from bid_purchase_order
        where is_valid=1 and is_terminate=0 and current_step not in
        <foreach collection="steps" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and contract_form_num in
        <foreach collection="formNums" item="formNum" separator="," open="(" close=")">
            #{formNum}
        </foreach>
    </select>

    <select id="getUserOrderFormNums" resultType="java.lang.String">
        select bpo.form_num
        from bid_purchase_order bpo
        join common_form cf on bpo.form_num=cf.form_num and cf.is_valid=1
        where (cf.creator_no=#{userId} or cf.be_entrusted_id=#{userId}) and bpo.is_valid=1 and bpo.is_terminate=0

        union

        select bpo.form_num
        from bid_purchase_order bpo
        join t_form_share_auth fsa on fsa.form_num=bpo.form_num and fsa.is_valid=1
        where ( (fsa.auth_value=#{userId} and fsa.auth_type='personal')
                <if test="deptIds!=null and deptIds.size()>0">
                    or (fsa.auth_value in
                    <foreach collection="deptIds" item="shareDeptId" separator="," open="(" close=")">
                        #{shareDeptId}
                    </foreach>
                    and fsa.auth_type='department')
                </if>
              )
    </select>

    <select id="findSupplierAndCurrencyByFormNums" resultType="com.xhs.oa.purchasebid.model.BidPurchaseOrder">
        select
          form_num formNum,
          supplier_name supplierName,
          currency currency
        from bid_purchase_order
        where form_num in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByContractAndSupplementFormNum" resultType="com.xhs.oa.purchasebid.model.BidPurchaseOrder">
        select <include refid="BpoBaseColumn"/>
        from bid_purchase_order bpo
        join common_form cf on bpo.form_num=cf.form_num and cf.is_valid=1
        where bpo.is_valid=1 and bpo.is_terminate=0 and bpo.contract_form_num in
        <foreach collection="contractFormNums" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and cf.audit_status in
        <foreach collection="auditStatus" item="status" separator="," open="(" close=")">
            #{status}
        </foreach>
    </select>

    <select id="selectBySpecialFormNum" resultType="com.xhs.oa.purchasebid.model.BidPurchaseOrder">
        select <include refid="BaseColumn"/>
        from bid_purchase_order
        where is_valid=1 and special_payment_form_num=#{specialFormNum}
    </select>

    <select id="queryFinishPaymentOrder" resultType="com.xhs.oa.purchasebid.dto.PaymentInvoiceNoticeJobDTO">
        select
            cf.process_instance_id procInstId,
            cf.form_num orderFormNum,
            bpo.supplier_name supplierName,
            cf.form_type formType,
            cf.creator_no creatorNo,
            cf.be_entrusted_id beEntrustedId,
            cf.amount orderAmount,
            bpo.payment_method paymentMethod
        from bid_purchase_order bpo
        join common_form cf on bpo.form_num=cf.form_num and cf.is_valid=1 and cf.audit_status=1
        where bpo.total_act_origin_amount=bpo.already_paid and bpo.invoice_difference!=0
        order by cf.id
        limit #{start}, #{pageSize}
    </select>

    <sql id="InvoicePageQuery">
        from bid_purchase_order bpo
        join common_form cf on bpo.form_num = cf.form_num
        where bpo.is_valid = 1 and cf.is_valid = 1 and bpo.invoice_difference > 0
        <if test="userId != null">
            and (bpo.creator_no = #{userId} or cf.be_entrusted_id = #{userId})
        </if>

        <if test="formNum != null and formNum != '' ">
            and bpo.form_num like concat('%',#{formNum},'%')
        </if>

        <if test="supplierName != null and supplierName != '' ">
            and bpo.supplier_name like concat('%',#{supplierName},'%')
        </if>

        <if test="beginAmount != null and endAmount != null">
            <![CDATA[ and bpo.total_origin_amount >= #{beginAmount} and bpo.total_origin_amount <= #{endAmount} ]]>
        </if>

        <if test="payType != null and payType != '' ">
            and bpo.payment_method = #{payType}
        </if>
    </sql>

    <sql id="PageQuery">
        from bid_purchase_order bpo
        join common_form cf on bpo.form_num=cf.form_num
        where cf.is_valid=1 and bpo.is_valid=1

        <if test="userId != null">
            and (cf.creator_no = #{userId} or cf.be_entrusted_id = #{userId})
        </if>

        <if test="formNum != null and formNum != '' ">
            and bpo.form_num like concat('%',#{formNum},'%')
        </if>

        <if test="formAuditStatus != null and formAuditStatus != '' ">
            and  cf.audit_status = #{formAuditStatus}
        </if>

        <if test="beginAmount != null and endAmount != null">
            <![CDATA[ and bpo.total_amount >= #{beginAmount} and bpo.total_amount <= #{endAmount} ]]>
        </if>

        <if test="supplierName != null and supplierName != '' ">
            and bpo.supplier_name like concat('%',#{supplierName},'%')
        </if>
    </sql>

    <insert id="insert" parameterType="com.xhs.oa.purchasebid.model.BidPurchaseOrder" useGeneratedKeys="true" keyProperty="id">
    insert into bid_purchase_order (
          form_num,
          order_form_type,
          contract_form_num,
          special_payment_form_num,
          supplier_name,
          payment_method,
          currency,
          total_origin_amount,
          total_act_origin_amount,
          total_amount,
          already_paid,
          invoice_difference,
          current_step,
          part_goods,
          is_confirm_delivery,
          is_apply_pay,
          is_terminate,
          is_valid,
          sent_invoice_notice,
          creator_no,
          creator,
          create_time,
          updator_no,
          updator,
          update_time
      )
    values (
           #{formNum},
           #{orderFormType},
           #{contractFormNum},
           #{specialPaymentFormNum},
           #{supplierName},
           #{paymentMethod},
           #{currency},
           #{totalOriginAmount},
           #{totalActOriginAmount},
           #{totalAmount},
           #{alreadyPaid},
           #{invoiceDifference},
           #{currentStep},
           #{partGoods},
           #{isConfirmDelivery},
           0,
           0,
           1,
           #{sentInvoiceNotice},
           #{creatorNo},
           #{creator},
           now(),
           #{updatorNo},
           #{updator},
           now()
      )
  </insert>

  
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidPurchaseOrder">
    update bid_purchase_order
    set
           special_payment_form_num = #{specialPaymentFormNum},

           total_origin_amount = #{totalOriginAmount},

           total_act_origin_amount = #{totalActOriginAmount},

           total_amount = #{totalAmount},

           invoice_difference = #{invoiceDifference},

           current_step = #{currentStep},

           part_goods = #{partGoods},

           is_confirm_delivery = #{isConfirmDelivery},

           updator_no = #{updatorNo},

           updator = #{updator},

           update_time = now()
    where id = #{id}
  </update>

    <update id="updateApplyPay">
        update bid_purchase_order
        set is_apply_pay = #{pay}, updator_no = #{userId}, updator = #{userName}, update_time=now()
        where is_valid=1 and is_terminate=0 and is_apply_pay=#{lastPay} and id=#{id}
    </update>

    <update id="batchUpdateDiffAmount">
        <foreach collection="list" item="item" separator=";">
            update bid_purchase_order
            set invoice_difference=#{item.invoiceDifference}, updator_no=#{item.updatorNo}, updator=#{item.updator}, update_time=now()
            where id=#{item.id}
        </foreach>
    </update>

    <update id="updateCurrentStep">
        update bid_purchase_order
        set current_step=#{nextStep}, updator_no = #{userId}, updator = #{userName}, update_time=now()
        where id=#{id} and is_valid=1 and is_terminate=0
    </update>

    <update id="updateCurrentStepAndIsApplyPay">
        update bid_purchase_order
        set current_step=#{nextStep}, is_apply_pay = #{isApplyPay}, updator_no = #{userId}, updator = #{userName}, update_time=now()
        where id=#{id} and is_valid=1 and is_terminate=0 and is_apply_pay = #{originApplyPay}
    </update>

    <update id="updateStepAndPayAndAlreadyPaid">
        update bid_purchase_order
        set current_step=#{nextStep}, is_apply_pay = #{isApplyPay}, already_paid=#{alreadyPaid}, updator_no = #{userId}, updator = #{userName}, update_time=now()
        where id=#{id} and is_valid=1 and is_terminate=0 and is_apply_pay = #{originApplyPay}
    </update>

    <update id="updateStepAndDelivery">
        update bid_purchase_order
        set current_step=#{nextStep}, is_confirm_delivery=#{isConfirmDelivery}, updator_no = #{userId}, updator = #{userName}, update_time=now()
        where id=#{id} and is_valid=1 and is_terminate=0
    </update>

    <update id="ternimateOrder">
        update bid_purchase_order
        set is_terminate=1, updator_no = #{userId}, updator = #{userName}, update_time=now()
        where form_num=#{formNum} and is_valid=1
    </update>

    <update id="updateOrderAfterSupplement">
        update bid_purchase_order
        set
          contract_form_num=#{contractFormNum},
          part_goods=#{partGoods},
          total_origin_amount=#{totalOriginAmount},
          total_amount=#{totalAmount},
          invoice_difference=#{invoiceDifference},
          updator_no = #{updatorNo},
          updator = #{updator},
          update_time=now()
        where id=#{id}
    </update>

    <update id="invalidByFormNum">
        update bid_purchase_order
        set is_valid=0, updator_no = #{userId}, updator = #{userName}, update_time=now()
        where is_valid=1 and form_num=#{formNum}
    </update>

    <update id="updateInvoiceDifference">
        update bid_purchase_order
        set invoice_difference=#{newDiff}, total_act_origin_amount=#{actOriginAmount}, updator_no = #{userId}, updator = #{userName}, update_time=now()
        where id=#{id} and is_valid=1 and invoice_difference=#{oldDiff}
    </update>

    <update id="updateCreator">
        update bid_purchase_order
        set creator=#{targetUserName}, creator_no=#{targetUserId}
        where form_num=#{formNum}
    </update>

    <update id="updateInvoiceDifferenceWithCheck">
        update bid_purchase_order
        set invoice_difference=#{newDiff}, updator_no = #{userId}, updator = #{userName}, update_time=now()
        where id=#{id} and is_valid=1 and invoice_difference=#{oldDiff}
    </update>

    <update id="updateIsConfirmDelivery">
        update bid_purchase_order
        set is_confirm_delivery=#{isConfirmDelivery}, updator_no = #{userId}, updator = #{userName}, update_time=now()
        where id=#{id} and is_valid=1 and is_terminate=0
    </update>

    <update id="updateIsApplyPay">
        update bid_purchase_order
        set is_apply_pay = #{isApplyPay}, updator_no = #{userId}, updator = #{userName}, update_time=now()
        where id=#{id} and is_valid=1 and is_terminate=0 and is_apply_pay = #{originApplyPay}
    </update>

    <update id="updateSendInvoiceNotice">
        update bid_purchase_order
        set sent_invoice_notice = 1
        where form_num=#{formNum};
    </update>

    <update id="updateIsApplyPayAndAlreadyPaidAmount">
        update bid_purchase_order
        set is_apply_pay = #{isApplyPay}, already_paid=#{alreadyPaid}, updator_no = #{userId}, updator = #{userName}, update_time=now()
        where id=#{id} and is_valid=1 and is_terminate=0 and is_apply_pay = #{originApplyPay}
    </update>

</mapper>   
