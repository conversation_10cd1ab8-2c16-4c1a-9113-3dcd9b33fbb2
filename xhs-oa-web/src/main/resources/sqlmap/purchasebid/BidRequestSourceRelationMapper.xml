<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.BidRequestSourceRelationMapper" > 

	<sql id="BaseColumn">
          id     id,
          request_form_num     requestFormNum,
          source_form_num     sourceFormNum,
          source_reason     sourceReason,
          is_valid     isValid,
          operator_no     operatorNo,
          operator     operator,
          create_time     createTime
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.purchasebid.model.BidRequestSourceRelation">
	    select 
	    <include refid="BaseColumn"/>
	    from bid_request_source_relation
	    where id = #{id}
	</select>

    <select id="selectBySourceFormNum" resultType="com.xhs.oa.purchasebid.model.BidRequestSourceRelation">
        select <include refid="BaseColumn"/>
        from bid_request_source_relation
        where source_form_num=#{sourceFormNum} and is_valid=1
    </select>


    <insert id="insert" parameterType="com.xhs.oa.purchasebid.model.BidRequestSourceRelation">
    insert into bid_request_source_relation (
          id,
          request_form_num,
          source_form_num,
          source_reason,
          is_valid,
          operator_no,
          operator,
          create_time
      )
    values (
           #{id},
           #{requestFormNum},
           #{sourceFormNum},
           #{sourceReason},
           #{isValid},
           #{operatorNo},
           #{operator},
          #{createTime}
      )
  </insert>

    <insert id="batchInsertRelationData" parameterType="java.util.List" keyProperty="id" useGeneratedKeys="true">
        insert into bid_request_source_relation (
          request_form_num,
          source_form_num,
          source_reason,
          is_valid,
          operator_no,
          operator,
          create_time
        )
        values
        <foreach collection="list" item="item" separator=",">
        (
           #{item.requestFormNum},
           #{item.sourceFormNum},
           #{item.sourceReason},
           1,
           #{item.operatorNo},
           #{item.operator},
           now()
        )
        </foreach>
    </insert>

    <update id="deleteRelationBySourceNo" >
        update bid_request_source_relation
        set is_valid = 0,
        operator_no = #{userId},
        operator = #{userName}
        where source_form_num = #{sourceNo} and is_valid=1
    </update>
  
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidRequestSourceRelation">
    update bid_request_source_relation
    set 
           id = #{id},

           request_form_num = #{requestFormNum},

           source_form_num = #{sourceFormNum},

           source_reason = #{sourceReason},

           is_valid = #{isValid},

           operator_no = #{operatorNo},

           operator = #{operator},

         create_time = #{createTime}
    where id = #{id}
  </update>

    <select id="queryPurchaseResourceNum" parameterType="java.util.List" resultType="com.xhs.oa.purchasebid.dto.PurchaseResouceNumDto">
        select
        bsr.request_form_num  formNum,
        count(1) recordCount
        from bid_request_source_relation bsr join
        common_form cf on bsr.source_form_num = cf.form_num and cf.audit_status in (1,2) and cf.is_valid = 1
        where bsr.is_valid = 1
        and bsr.request_form_num in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        group by bsr.request_form_num
    </select>


    <select id="queryPurchaseRequestBySourceNos" parameterType="java.util.List" resultType="com.xhs.oa.purchasebid.model.BidRequestSourceRelation">
        select
        request_form_num requestFormNum,
        source_form_num sourceFormNum
        from bid_request_source_relation
        where is_valid = 1 and source_form_num in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="queryPurchaseRelationByRequestFormNo" resultType="com.xhs.oa.purchasebid.model.BidRequestSourceRelation">
        select
        <include refid="BaseColumn"/>
        from bid_request_source_relation
        where request_form_num = #{formNo}
        and is_valid = 1
        order by id desc
    </select>

    <select id="getRelateSourceFormsByPrForm" resultType="java.lang.String">
        select source_form_num
        from bid_request_source_relation brsr
        join bid_purchase_source bps on brsr.source_form_num=bps.form_num
        join common_form cf on brsr.source_form_num=cf.form_num
        where brsr.is_valid=1 and cf.is_valid=1 and bps.is_valid=1
          and bps.is_suspend=0 and cf.audit_status=#{auditStatus} and request_form_num=#{prFormNum}
    </select>

    <select id="getRelatePrFormsBySourceForm" resultType="java.lang.String">
        select request_form_num
        from bid_request_source_relation
        where is_valid=1 and source_form_num=#{sourceFormNum}
    </select>

    <select id="findInAuditSourceFormByPr" resultType="com.xhs.oa.purchasebid.model.BidRequestSourceRelation">
        select
          brsr.request_form_num     requestFormNum,
          brsr.source_form_num     sourceFormNum
        from bid_request_source_relation brsr
        join common_form cf on brsr.source_form_num=cf.form_num and cf.is_valid=1
        where brsr.is_valid=1 and cf.audit_status=#{status} and brsr.request_form_num=#{formNum}
    </select>

    <select id="findSubmitSourceFormByPr" resultType="java.lang.Integer">
        select count(1)
        from bid_request_source_relation brsr
        join common_form cf on brsr.source_form_num=cf.form_num and cf.is_valid=1
        where brsr.is_valid=1 and brsr.request_form_num=#{formNum} and cf.audit_status in
        <foreach collection="statusList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

</mapper>   
