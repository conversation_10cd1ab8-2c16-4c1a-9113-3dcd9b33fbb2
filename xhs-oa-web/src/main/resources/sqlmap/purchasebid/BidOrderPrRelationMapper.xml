<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.BidOrderPrRelationMapper" > 

	<sql id="BaseColumn">
          id     id,
          purchase_order_id     purchaseOrderId,
          pr_form_num     prFormNum,
          share_amount     shareAmount,
          is_valid     isValid,
          creator_no     creator<PERSON><PERSON>,
          creator     creator,
          create_time     createTime,
          update_time     updateTime
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.purchasebid.model.BidOrderPrRelation">
	    select 
	    <include refid="BaseColumn"/>
	    from bid_order_pr_relation
	    where id = #{id}
	</select>

    <select id="selectByOrderId" resultType="com.xhs.oa.purchasebid.model.BidOrderPrRelation">
        select <include refid="BaseColumn"/>
        from bid_order_pr_relation
        where is_valid=1 and purchase_order_id=#{orderId}
    </select>

    <select id="selectByPrFormNum" resultType="com.xhs.oa.purchasebid.model.BidOrderPrRelation">
        select
        <include refid="BaseColumn"/>
        from bid_order_pr_relation
        where is_valid=1 and pr_form_num=#{prFormNum}
    </select>


    <insert id="insert" parameterType="com.xhs.oa.purchasebid.model.BidOrderPrRelation">
    insert into bid_order_pr_relation (
          id,
          purchase_order_id,
          pr_form_num,
          share_amount,
          is_valid,
          creator_no,
          creator,
          create_time,
          update_time
      )
    values (
           #{id},
           #{purchaseOrderId},
           #{prFormNum},
           #{shareAmount},
           #{isValid},
           #{creatorNo},
           #{creator},
           #{createTime},
          #{updateTime}
      )
  </insert>

    <insert id="batchInsert">
        insert into bid_order_pr_relation (
          purchase_order_id,
          pr_form_num,
          share_amount,
          is_valid,
          creator_no,
          creator,
          create_time,
          update_time
      )
    values
    <foreach collection="list" item="item" separator=",">
    (
           #{item.purchaseOrderId},
           #{item.prFormNum},
           #{item.shareAmount},
           1,
           #{item.creatorNo},
           #{item.creator},
           now(),
           now()
      )
    </foreach>
    </insert>


    <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidOrderPrRelation">
    update bid_order_pr_relation
    set 
           id = #{id},

           purchase_order_id = #{purchaseOrderId},

           pr_form_num = #{prFormNum},

           share_amount = #{shareAmount},

           is_valid = #{isValid},

           creator_no = #{creatorNo},

           creator = #{creator},

           create_time = #{createTime},

         update_time = #{updateTime}
    where id = #{id}
  </update>

    <update id="invalidateByOrderId">
        update bid_order_pr_relation
        set is_valid=0, update_time=now()
        where purchase_order_id=#{orderId}
    </update>

</mapper>   
