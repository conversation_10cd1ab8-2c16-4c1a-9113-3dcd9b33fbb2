<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.BidJudgeEntrustMapper" > 

	<sql id="BaseColumn">
          id     id,
          rating_judge_id     ratingJudgeId,
          entrust_judge_id     entrustJudgeId,
          entrusted_reason     entrustedReason,
          operator_no     operatorNo,
          operator     operator,
          create_time     createTime
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.purchasebid.model.BidJudgeEntrust">
	    select 
	    <include refid="BaseColumn"/>
	    from bid_judge_entrust
	    where id = #{id}
	</select>

    <select id="selectByEntrustJudgeIds" resultType="com.xhs.oa.purchasebid.model.BidJudgeEntrust">
        select
        <include refid="BaseColumn"/>
        from bid_judge_entrust
        where rating_judge_id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>


    <insert id="insert" parameterType="com.xhs.oa.purchasebid.model.BidJudgeEntrust">
    insert into bid_judge_entrust (
          id,
          rating_judge_id,
          entrust_judge_id,
          entrusted_reason,
          operator_no,
          operator,
          create_time
      )
    values (
           #{id},
           #{ratingJudgeId},
           #{entrustJudgeId},
           #{entrustedReason},
           #{operatorNo},
           #{operator},
          #{createTime}
      )
  </insert>


    <insert id="batchInsertBidJudgeEntrust" parameterType="java.util.List">
        insert into bid_judge_entrust (
          rating_judge_id,
          entrust_judge_id,
          entrusted_reason,
          operator_no,
          operator,
          create_time
          )
        values
        <foreach collection="list" item="item" separator=",">
          (
               #{item.ratingJudgeId},
               #{item.entrustJudgeId},
               #{item.entrustedReason},
               #{item.operatorNo},
               #{item.operator},
               now()
          )
        </foreach>
    </insert>

  
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidJudgeEntrust">
    update bid_judge_entrust
    set 
           id = #{id},

           rating_judge_id = #{ratingJudgeId},

           entrust_judge_id = #{entrustJudgeId},

           entrusted_reason = #{entrustedReason},

           operator_no = #{operatorNo},

           operator = #{operator},

         create_time = #{createTime}
    where id = #{id}
  </update>

</mapper>   
