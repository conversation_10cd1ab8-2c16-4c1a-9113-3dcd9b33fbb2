<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.BidPoPrRelationMapper">


    <select id="queryListByPoNum" resultType="com.xhs.oa.purchasebid.model.BidPoPrRelation">
        SELECT id
             , order_form_num orderFormNum
             , pr_form_num prFormNum
             , order_amount orderAmount
             , is_valid isValid
             , creator_no creatorNo
             , creator
             , create_time createTime
             , updator_no updatorNo
             , updator
             , update_time updateTime
        from bid_po_pr_relation
        WHERE order_form_num =  #{poNum}
          AND is_valid = '1'
    </select>
</mapper>
