<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.BidPurchaseBiddingMapper" > 

	<sql id="BaseColumn">
          id     id,
          source_id     sourceId,
          is_apply_avoid_bond     isApplyAvoidBond,
          avoid_bond_remark     avoidBondRemark,
          bond_receive_endtime     bondReceiveEndtime,
          bid_evaluation_method     bidEvaluationMethod,
          start_time     startTime,
          end_time     endTime,
          bidding_address     biddingAddress,
          rating_type     ratingType,
          supervisor_id     supervisorId,
          is_supervisor_confirmed     isSupervisorConfirmed,
          is_start_rating     isStartRating,
          create_time     createTime,
          creator_no  creatorNo,
          creator  creator,
          update_time  updateTime,
          updator_no  updatorNo,
          updator  updator,
          bidding_name  biddingName,
          result_time  resultTime,
          contact  contact
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.purchasebid.model.BidPurchaseBidding">
	    select 
	    <include refid="BaseColumn"/>
	    from bid_purchase_bidding
	    where id = #{id}
	</select>

    <select id="selectBySourceId" resultType="com.xhs.oa.purchasebid.model.BidPurchaseBidding">
        select <include refid="BaseColumn"/>
        from bid_purchase_bidding
        where source_id=#{sourceId}
    </select>

    <select id="findWaidBondEndTimes" resultType="com.xhs.oa.purchasebid.dto.WaitBondDto">
        select
          bps.form_num formNum,
          bpb.bond_receive_endtime bondReceiveEndTime
        from bid_purchase_source bps
        join bid_purchase_bidding bpb on bpb.source_id=bps.id
        where bps.is_valid=1 and bps.form_num in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insert" parameterType="com.xhs.oa.purchasebid.model.BidPurchaseBidding" keyProperty="id" useGeneratedKeys="true">
    insert into bid_purchase_bidding (
          source_id,
          is_apply_avoid_bond,
          avoid_bond_remark,
          bond_receive_endtime,
          bid_evaluation_method,
          start_time,
          end_time,
          bidding_address,
          rating_type,
          supervisor_id,
          is_supervisor_confirmed,
          is_start_rating,
          create_time,
          creator_no,
          creator,
          update_time,
          updator_no,
          updator,
          bidding_name,
          result_time,
          contact
      )
    values (
           #{sourceId},
           #{isApplyAvoidBond},
           #{avoidBondRemark},
           #{bondReceiveEndtime},
           #{bidEvaluationMethod},
           #{startTime},
           #{endTime},
           #{biddingAddress},
           #{ratingType},
           #{supervisorId},
           #{isSupervisorConfirmed},
           #{isStartRating},
           now(),
           #{creatorNo},
           #{creator},
           now(),
           #{updatorNo},
           #{updator},
           #{biddingName},
           #{resultTime},
           #{contact}
      )
  </insert>

  
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidPurchaseBidding">
    update bid_purchase_bidding
    set 

           source_id = #{sourceId},

           is_apply_avoid_bond = #{isApplyAvoidBond},

           avoid_bond_remark = #{avoidBondRemark},

           bond_receive_endtime = #{bondReceiveEndtime},

           bid_evaluation_method = #{bidEvaluationMethod},

           start_time = #{startTime},

           end_time = #{endTime},

           bidding_address = #{biddingAddress},

           rating_type = #{ratingType},

           supervisor_id = #{supervisorId},

           is_supervisor_confirmed = #{isSupervisorConfirmed},

           is_start_rating = #{isStartRating},

           updator_no = #{updatorNo},

           updator = #{updator},

           update_time = now()

      where id = #{id}
  </update>

    <update id="updateBiddingInfoByPrimaryKey">
    update bid_purchase_bidding
    set
           start_time = #{startTime},

           end_time = #{endTime},

           bidding_address = #{biddingAddress},

           rating_type = #{ratingType},

           supervisor_id = #{supervisorId},

           updator_no = #{updatorNo},

           updator = #{updator},

           bidding_name = #{biddingName},

           result_time = #{resultTime},

           contact = #{contact}

    where id = #{id}
    </update>

    <update id="updateApplyAvoidBond">
        update bid_purchase_bidding
        set is_apply_avoid_bond=#{isApply}, avoid_bond_remark=#{remark}, updator_no=#{userId}, updator=#{userName}
        where id=#{id}
    </update>

</mapper>   
