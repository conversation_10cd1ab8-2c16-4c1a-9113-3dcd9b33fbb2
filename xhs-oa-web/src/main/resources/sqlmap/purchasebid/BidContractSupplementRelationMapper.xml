<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.BidContractSupplementRelationMapper" > 

	<sql id="BaseColumn">
          id     id,
          contract_form_num     contractFormNum,
          supplement_form_num     supplementFormNum,
          creator_no     creator<PERSON><PERSON>,
          creator     creator,
          create_time     createTime,
          update_time     updateTime
    </sql>

    <delete id="deleteRelation">
        delete from bid_contract_supplement_relation
        where supplement_form_num=#{supplementFormNum}
    </delete>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.purchasebid.model.BidContractSupplementRelation">
	    select 
	    <include refid="BaseColumn"/>
	    from bid_contract_supplement_relation
	    where id = #{id}
	</select>

    <select id="findLatestSupplementFormNum" resultType="java.lang.String">
        select bcsr.supplement_form_num
        from bid_contract_supplement_relation bcsr
        join common_form cf on bcsr.supplement_form_num=cf.form_num and cf.is_valid=1 and cf.audit_status=#{status}
        where bcsr.contract_form_num=#{contractFormNum}
        order by bcsr.id desc limit 1
    </select>

    <select id="findAllValidContractSupplementFormNums" resultType="java.lang.String">
        select bcsr.supplement_form_num
        from bid_contract_supplement_relation bcsr
        join common_form cf on bcsr.supplement_form_num=cf.form_num and cf.is_valid=1
        where cf.audit_status=#{auditStatus} and bcsr.contract_form_num=#{contractFormNum}
    </select>

    <select id="selectBySupplementFormNum" resultType="com.xhs.oa.purchasebid.model.BidContractSupplementRelation">
        select <include refid="BaseColumn"/>
        from bid_contract_supplement_relation
        where supplement_form_num=#{supplementFormNum}
    </select>

    <select id="findLatestSupplementForm" resultType="com.xhs.oa.form.model.CommonForm">
        select
          cf.form_num formNum,
          cf.audit_status auditStatus,
          cf.process_instance_id processInstanceId,
          cf.form_content formContent
        from bid_contract_supplement_relation bcsr
        join common_form cf on bcsr.supplement_form_num=cf.form_num and cf.is_valid=1
        where bcsr.contract_form_num=#{contractFormNum}
        order by bcsr.id desc limit 1
    </select>

    <select id="querySupplements" resultType="com.xhs.oa.form.model.CommonForm">
        select
            cf.form_num formNum,
            cf.audit_status auditStatus
        from bid_contract_supplement_relation csr
        join common_form cf on cf.form_num=csr.supplement_form_num and cf.is_valid=1
        where csr.contract_form_num in
        <foreach collection="list" item="contractFormNum" separator="," open="(" close=")">
            #{contractFormNum}
        </foreach>
    </select>


    <insert id="insert" parameterType="com.xhs.oa.purchasebid.model.BidContractSupplementRelation">
    insert into bid_contract_supplement_relation (
          contract_form_num,
          supplement_form_num,
          creator_no,
          creator,
          create_time,
          update_time
      )
    values (
           #{contractFormNum},
           #{supplementFormNum},
           #{creatorNo},
           #{creator},
           now(),
           now()
      )
  </insert>

  
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidContractSupplementRelation">
    update bid_contract_supplement_relation
    set 
           id = #{id},

           contract_form_num = #{contractFormNum},

           supplement_form_num = #{supplementFormNum},

           creator_no = #{creatorNo},

           creator = #{creator},

           create_time = #{createTime},

         update_time = #{updateTime}
    where id = #{id}
  </update>

</mapper>   
