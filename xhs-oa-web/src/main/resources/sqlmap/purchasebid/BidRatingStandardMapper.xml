<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.BidRatingStandardMapper" > 

	<sql id="BaseColumn">
          id     id,
          rating_item_id     ratingItemId,
          standard_desc     standardDesc,
          standard_percentage     standardPercentage,
          operator_no     operatorNo,
          operator     operator,
          create_time     createTime
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.purchasebid.model.BidRatingStandard">
	    select 
	    <include refid="BaseColumn"/>
	    from bid_rating_standard
	    where id = #{id}
	</select>

    <select id="selectByItemIds" resultType="com.xhs.oa.purchasebid.model.BidRatingStandard">
        select <include refid="BaseColumn"/>
        from bid_rating_standard
        where rating_item_id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="findAllScoredSupplierIdsByJudgeId" resultType="java.lang.Long">
        select
          distinct brr.supplier_id
        from t_supplier_service bsi
        join bid_rating_result brr on brr.supplier_id=bsi.id
        where bsi.source_form_num=#{sourceFormNum} and brr.judge_id=#{judgeId}
    </select>

    <select id="findAllVerifiedSupplierIdsByJudgeId" resultType="java.lang.Long">
        select
          distinct brr.supplier_id
        from t_supplier_service bsi
        join bid_rating_result brr on brr.supplier_id=bsi.id
        where bsi.source_form_num=#{sourceFormNum} and brr.judge_id=#{judgeId} and brr.old_score!=-1
    </select>


    <insert id="insert" parameterType="com.xhs.oa.purchasebid.model.BidRatingStandard">
    insert into bid_rating_standard (
          id,
          rating_item_id,
          standard_desc,
          standard_percentage,
          operator_no,
          operator,
          create_time
      )
    values (
           #{id},
           #{ratingItemId},
           #{standardDesc},
           #{standardPercentage},
           #{operatorNo},
           #{operator},
          #{createTime}
      )
  </insert>

    <insert id="batchInsert">
     insert into bid_rating_standard (
          rating_item_id,
          standard_title_id,
          standard,
          operator_no,
          operator,
          create_time
      ) values
      <foreach collection="list" item="item" separator=",">
          (
          #{item.ratingItemId},
          #{item.standardTitleId},
          #{item.standard},
          #{item.operatorNo},
          #{item.operator},
          now()
          )
      </foreach>
    </insert>


    <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidRatingStandard">
    update bid_rating_standard
    set 
           id = #{id},

           rating_item_id = #{ratingItemId},

           standard_desc = #{standardDesc},

           standard_percentage = #{standardPercentage},

           operator_no = #{operatorNo},

           operator = #{operator},

         create_time = #{createTime}
    where id = #{id}
  </update>

</mapper>   
