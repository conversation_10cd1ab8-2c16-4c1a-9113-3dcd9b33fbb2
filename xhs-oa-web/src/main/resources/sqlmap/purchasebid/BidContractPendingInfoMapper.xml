<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhs.oa.purchasebid.mapper.BidContractPendingInfoMapper">
  <resultMap id="BaseResultMap" type="com.xhs.oa.purchasebid.model.BidContractPendingInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="contract_form_num" jdbcType="VARCHAR" property="contractFormNum" />
    <result column="contract_name" jdbcType="VARCHAR" property="contractName" />
    <result column="current_step" jdbcType="VARCHAR" property="currentStep" />
    <result column="contract_status" jdbcType="TINYINT" property="contractStatus" />
    <result column="is_valid" jdbcType="TINYINT" property="isValid" />
    <result column="contract_amount" jdbcType="DECIMAL" property="contractAmount" />
    <result column="our_company" jdbcType="VARCHAR" property="ourCompany" />
    <result column="counter_company" jdbcType="VARCHAR" property="counterCompany" />
    <result column="department_id_path" jdbcType="VARCHAR" property="departmentIdPath" />
    <result column="is_in_account" jdbcType="TINYINT" property="isInAccount" />
    <result column="department_name_path" jdbcType="VARCHAR" property="departmentNamePath" />
    <result column="creator_no" jdbcType="VARCHAR" property="creatorNo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updator_no" jdbcType="VARCHAR" property="updatorNo" />
    <result column="updator" jdbcType="VARCHAR" property="updator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="finance_user_no" jdbcType="VARCHAR" property="financeUserNo" />
    <result column="save_or_submit" jdbcType="TINYINT" property="saveOrSubmit" />
  </resultMap>
  <sql id="Base_Column_List">
    id, contract_form_num, contract_name, current_step, contract_status, is_valid, contract_amount, 
    our_company, counter_company, department_id_path, is_in_account, department_name_path, 
    creator_no, creator, create_time, updator_no, updator, update_time, finance_user_no,save_or_submit
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bid_contract_pending_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bid_contract_pending_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.xhs.oa.purchasebid.model.BidContractPendingInfo" useGeneratedKeys="true">
    insert into bid_contract_pending_info (contract_form_num, contract_name, current_step, 
      contract_status, is_valid, contract_amount, 
      our_company, counter_company, department_id_path, 
      is_in_account, department_name_path, creator_no, 
      creator, create_time, updator_no, 
      updator, update_time, finance_user_no,save_or_submit
      )
    values (#{contractFormNum,jdbcType=VARCHAR}, #{contractName,jdbcType=VARCHAR}, #{currentStep,jdbcType=VARCHAR}, 
      #{contractStatus,jdbcType=TINYINT}, #{isValid,jdbcType=TINYINT}, #{contractAmount,jdbcType=DECIMAL}, 
      #{ourCompany,jdbcType=VARCHAR}, #{counterCompany,jdbcType=VARCHAR}, #{departmentIdPath,jdbcType=VARCHAR}, 
      #{isInAccount,jdbcType=TINYINT}, #{departmentNamePath,jdbcType=VARCHAR}, #{creatorNo,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updatorNo,jdbcType=VARCHAR}, 
      #{updator,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{financeUserNo,jdbcType=VARCHAR},#{saveOrSubmit,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.xhs.oa.purchasebid.model.BidContractPendingInfo" useGeneratedKeys="true">
    insert into bid_contract_pending_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="contractFormNum != null">
        contract_form_num,
      </if>
      <if test="contractName != null">
        contract_name,
      </if>
      <if test="currentStep != null">
        current_step,
      </if>
      <if test="contractStatus != null">
        contract_status,
      </if>
      <if test="isValid != null">
        is_valid,
      </if>
      <if test="contractAmount != null">
        contract_amount,
      </if>
      <if test="ourCompany != null">
        our_company,
      </if>
      <if test="counterCompany != null">
        counter_company,
      </if>
      <if test="departmentIdPath != null">
        department_id_path,
      </if>
      <if test="isInAccount != null">
        is_in_account,
      </if>
      <if test="departmentNamePath != null">
        department_name_path,
      </if>
      <if test="creatorNo != null">
        creator_no,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updatorNo != null">
        updator_no,
      </if>
      <if test="updator != null">
        updator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="financeUserNo != null">
        finance_user_no,
      </if>
      <if test="saveOrSubmit != null">
        save_or_submit,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="contractFormNum != null">
        #{contractFormNum,jdbcType=VARCHAR},
      </if>
      <if test="contractName != null">
        #{contractName,jdbcType=VARCHAR},
      </if>
      <if test="currentStep != null">
        #{currentStep,jdbcType=VARCHAR},
      </if>
      <if test="contractStatus != null">
        #{contractStatus,jdbcType=TINYINT},
      </if>
      <if test="isValid != null">
        #{isValid,jdbcType=TINYINT},
      </if>
      <if test="contractAmount != null">
        #{contractAmount,jdbcType=DECIMAL},
      </if>
      <if test="ourCompany != null">
        #{ourCompany,jdbcType=VARCHAR},
      </if>
      <if test="counterCompany != null">
        #{counterCompany,jdbcType=VARCHAR},
      </if>
      <if test="departmentIdPath != null">
        #{departmentIdPath,jdbcType=VARCHAR},
      </if>
      <if test="isInAccount != null">
        #{isInAccount,jdbcType=TINYINT},
      </if>
      <if test="departmentNamePath != null">
        #{departmentNamePath,jdbcType=VARCHAR},
      </if>
      <if test="creatorNo != null">
        #{creatorNo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorNo != null">
        #{updatorNo,jdbcType=VARCHAR},
      </if>
      <if test="updator != null">
        #{updator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="financeUserNo != null">
        #{financeUserNo,jdbcType=VARCHAR},
      </if>
      <if test="saveOrSubmit != null">
        #{saveOrSubmit,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhs.oa.purchasebid.model.BidContractPendingInfo">
    update bid_contract_pending_info
    <set>
      <if test="contractFormNum != null">
        contract_form_num = #{contractFormNum,jdbcType=VARCHAR},
      </if>
      <if test="contractName != null">
        contract_name = #{contractName,jdbcType=VARCHAR},
      </if>
      <if test="currentStep != null">
        current_step = #{currentStep,jdbcType=VARCHAR},
      </if>
      <if test="contractStatus != null">
        contract_status = #{contractStatus,jdbcType=TINYINT},
      </if>
      <if test="isValid != null">
        is_valid = #{isValid,jdbcType=TINYINT},
      </if>
      <if test="contractAmount != null">
        contract_amount = #{contractAmount,jdbcType=DECIMAL},
      </if>
      <if test="ourCompany != null">
        our_company = #{ourCompany,jdbcType=VARCHAR},
      </if>
      <if test="counterCompany != null">
        counter_company = #{counterCompany,jdbcType=VARCHAR},
      </if>
      <if test="departmentIdPath != null">
        department_id_path = #{departmentIdPath,jdbcType=VARCHAR},
      </if>
      <if test="isInAccount != null">
        is_in_account = #{isInAccount,jdbcType=TINYINT},
      </if>
      <if test="departmentNamePath != null">
        department_name_path = #{departmentNamePath,jdbcType=VARCHAR},
      </if>
      <if test="creatorNo != null">
        creator_no = #{creatorNo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorNo != null">
        updator_no = #{updatorNo,jdbcType=VARCHAR},
      </if>
      <if test="updator != null">
        updator = #{updator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="financeUserNo != null">
        finance_user_no = #{financeUserNo,jdbcType=VARCHAR},
      </if>
      <if test="saveOrSubmit != null">
        save_or_submit = #{saveOrSubmit,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidContractPendingInfo">
    update bid_contract_pending_info
    set contract_form_num = #{contractFormNum,jdbcType=VARCHAR},
      contract_name = #{contractName,jdbcType=VARCHAR},
      current_step = #{currentStep,jdbcType=VARCHAR},
      contract_status = #{contractStatus,jdbcType=TINYINT},
      is_valid = #{isValid,jdbcType=TINYINT},
      contract_amount = #{contractAmount,jdbcType=DECIMAL},
      our_company = #{ourCompany,jdbcType=VARCHAR},
      counter_company = #{counterCompany,jdbcType=VARCHAR},
      department_id_path = #{departmentIdPath,jdbcType=VARCHAR},
      is_in_account = #{isInAccount,jdbcType=TINYINT},
      department_name_path = #{departmentNamePath,jdbcType=VARCHAR},
      creator_no = #{creatorNo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updator_no = #{updatorNo,jdbcType=VARCHAR},
      updator = #{updator,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      finance_user_no = #{financeUserNo,jdbcType=VARCHAR},
      save_or_submit= #{saveOrSubmit,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="selectByContractFormNum" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bid_contract_pending_info
    where contract_form_num = #{contractFormNum} and is_valid = 1
  </select>
  
  <select id="queryContractInfoByCondition" parameterType="com.xhs.oa.purchasebid.param.ContractPendingSearchParam" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from bid_contract_pending_info
    <where>
       is_valid = 1
      <if test="contractFormNum != null and contractFormNum != ''">
        and contract_form_num like concat('%',#{contractFormNum},'%')
      </if>
      <if test="contractName != null and contractName != ''">
        and contract_name like concat('%',#{contractName},'%')
      </if>
      <if test="creatorNo != null and creatorNo != ''">
        and creator_no = #{creatorNo}
      </if>
      <if test="ourCompany != null and ourCompany != ''">
        and our_company like concat('%',#{ourCompany},'%')
      </if>
      <if test="counterCompany != null and counterCompany != ''">
        and counter_company like concat('%',#{counterCompany},'%')
      </if>
      <if test="financeUserNo != null and financeUserNo != ''">
        and finance_user_no like concat('%',#{financeUserNo},'%')
      </if>
      <if test="contractStatus != null">
        and contract_status = #{contractStatus}
      </if>
      <if test="departmentIdPath != null and departmentIdPath != ''">
        and department_id_path like concat('%',#{departmentIdPath},'%')
      </if>
      <if test="lastUpdateNo != null and lastUpdateNo !=''">
        and updator_no = #{lastUpdateNo}
      </if>
    </where>
    order by create_time desc
    limit #{start}, #{pageSize}
  </select>

  <select id="queryContractInfoCountByCondition" parameterType="com.xhs.oa.purchasebid.param.ContractPendingSearchParam" resultType="int">
    select count(*)
    from bid_contract_pending_info
    <where>
      is_valid = 1
      <if test="contractFormNum != null and contractFormNum != ''">
        and contract_form_num like concat('%',#{contractFormNum},'%')
      </if>
      <if test="contractName != null and contractName != ''">
        and contract_name like concat('%',#{contractName},'%')
      </if>
      <if test="creatorNo != null and creatorNo != ''">
        and creator_no = #{creatorNo}
      </if>
      <if test="ourCompany != null and ourCompany != ''">
        and our_company like concat('%',#{ourCompany},'%')
      </if>
      <if test="counterCompany != null and counterCompany != ''">
        and counter_company like concat('%',#{counterCompany},'%')
      </if>
      <if test="financeUserNo != null and financeUserNo != ''">
        and finance_user_no like concat('%',#{financeUserNo},'%')
      </if>
      <if test="contractStatus != null">
        and contract_status = #{contractStatus}
      </if>
      <if test="departmentIdPath != null and departmentIdPath != ''">
        and department_id_path like concat('%',#{departmentIdPath},'%')
      </if>
      <if test="lastUpdateNo != null and lastUpdateNo !=''">
        and updator_no = #{lastUpdateNo}
      </if>
  </where>
  </select>
</mapper>