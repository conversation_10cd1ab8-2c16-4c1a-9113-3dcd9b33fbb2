<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.BidRatingItemMapper" > 

	<sql id="BaseColumn">
          id     id,
          bidding_id     biddingId,
          rating_item_name     ratingItemName,
          rating_item_type     ratingItemType,
          rating_dimension     ratingDimension,
          total_score     totalScore,
          is_valid     isValid,
          operator_no     operatorNo,
          operator     operator,
          create_time     createTime
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.purchasebid.model.BidRatingItem">
	    select 
	    <include refid="BaseColumn"/>
	    from bid_rating_item
	    where id = #{id}
	</select>

    <select id="selectByBiddingIdAndItemType" resultType="com.xhs.oa.purchasebid.model.BidRatingItem">
        select <include refid="BaseColumn"/>
        from bid_rating_item
        where is_valid=1 and bidding_id=#{biddingId} and rating_item_type=#{type}
    </select>

    <select id="findBiddingStandards" resultType="com.xhs.oa.purchasebid.dto.BidRatingItemDto">
        select
          bri.id itemId,
          bri.rating_item_type itemType,
          bri.rating_item_name itemName,
          bri.rating_dimension dimension,
          bri.total_score totalScore,
          brs.standard_title_id standardTitleId,
          brs.standard standard
        from bid_rating_item bri
        left join bid_rating_standard brs on brs.rating_item_id=bri.id
        where bri.is_valid=1 and bri.bidding_id=#{biddingId}
    </select>

    <select id="findAllRatingItems" resultType="com.xhs.oa.purchasebid.model.BidRatingItem">
        select
          id id,
          rating_item_type ratingItemType,
          total_score totalScore
        from bid_rating_item
        where is_valid=1 and bidding_id=#{biddingId}
    </select>

    <select id="findBiddingStandardsByItemType" resultType="com.xhs.oa.purchasebid.dto.BidRatingItemDto">
        select
          bri.id itemId,
          bri.rating_item_type itemType,
          bri.rating_item_name itemName,
          bri.rating_dimension dimension,
          bri.total_score totalScore,
          brs.standard_title_id standardTitleId,
          brs.standard standard
        from bid_rating_item bri
        join bid_rating_standard brs on brs.rating_item_id=bri.id
        where bri.is_valid=1 and bri.rating_item_type=#{type} and bri.bidding_id=#{biddingId}
    </select>


    <insert id="insert" parameterType="com.xhs.oa.purchasebid.model.BidRatingItem" useGeneratedKeys="true" keyProperty="id">
    insert into bid_rating_item (
          bidding_id,
          rating_item_name,
          rating_item_type,
          rating_dimension,
          total_score,
          is_valid,
          operator_no,
          operator,
          create_time
      )
    values (
           #{biddingId},
           #{ratingItemName},
           #{ratingItemType},
           #{ratingDimension},
           #{totalScore},
           1,
           #{operatorNo},
           #{operator},
           now()
      )
  </insert>

  
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidRatingItem">
    update bid_rating_item
    set 
           id = #{id},

           bidding_id = #{biddingId},

           rating_item_name = #{ratingItemName},

           rating_dimension = #{ratingDimension},

           total_score = #{totalScore},

           is_valid = #{isValid},

           operator_no = #{operatorNo},

           operator = #{operator},

         create_time = #{createTime}
    where id = #{id}
  </update>

    <update id="invalidateByBiddingId">
        update bid_rating_item
        set is_valid=0
        where is_valid=1 and bidding_id=#{biddingId}
    </update>

</mapper>   
