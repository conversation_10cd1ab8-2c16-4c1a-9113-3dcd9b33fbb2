<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.BidRequestAmountRelationMapper" > 

	<sql id="BaseColumn">
          id     id,
          pr_form_num     prFormNum,
          change_form_num     changeForm<PERSON>um,
          creator_no     creator<PERSON><PERSON>,
          creator     creator,
          create_time     createTime
    </sql>

    <delete id="deleteByChangeFormNum">
        delete from bid_request_amount_relation
        where change_form_num=#{formNum}
    </delete>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.purchasebid.model.BidRequestAmountRelation">
	    select 
	    <include refid="BaseColumn"/>
	    from bid_request_amount_relation
	    where id = #{id}
	</select>

    <select id="queryInAuditPrForm" resultType="com.xhs.oa.purchasebid.model.BidRequestAmountRelation">
        select
        brar.pr_form_num     prFormNum,
        brar.change_form_num     changeFormNum
        from bid_request_amount_relation brar
        join common_form cf on brar.change_form_num=cf.form_num and cf.is_valid=1
        where cf.audit_status=#{status} and brar.pr_form_num in
        <foreach collection="prFormNums" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>
    </select>


    <insert id="insert" parameterType="com.xhs.oa.purchasebid.model.BidRequestAmountRelation">
    insert into bid_request_amount_relation (
          pr_form_num,
          change_form_num,
          creator_no,
          creator,
          create_time
      )
    values (
           #{prFormNum},
           #{changeFormNum},
           #{creatorNo},
           #{creator},
           now()
      )
  </insert>

  
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidRequestAmountRelation">
    update bid_request_amount_relation
    set 
           id = #{id},

           pr_form_num = #{prFormNum},

           change_form_num = #{changeFormNum},

           creator_no = #{creatorNo},

           creator = #{creator},

         create_time = #{createTime}
    where id = #{id}
  </update>

</mapper>   
