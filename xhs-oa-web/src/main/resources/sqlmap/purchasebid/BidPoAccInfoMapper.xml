<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhs.oa.purchasebid.mapper.BidPoAccInfoMapper">
  <resultMap id="BaseResultMap" type="com.xhs.oa.purchasebid.model.BidPoAccInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="po_form_num" jdbcType="VARCHAR" property="poFormNum" />
    <result column="acc_form_num" jdbcType="VARCHAR" property="accFormNum" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="pr_from_num" jdbcType="VARCHAR" property="prFromNum" />
    <result column="tax_rate" jdbcType="VARCHAR" property="taxRate" />
    <result column="subject_remake" jdbcType="VARCHAR" property="subjectRemake" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="retain_amount" jdbcType="DECIMAL" property="retainAmount" />
    <result column="our_party" jdbcType="VARCHAR" property="ourParty" />
    <result column="other_party" jdbcType="VARCHAR" property="otherParty" />
    <result column="attribution_month" jdbcType="VARCHAR" property="attributionMonth" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_valid" jdbcType="TINYINT" property="isValid" />
    <result column="creator_no" jdbcType="VARCHAR" property="creatorNo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updator_no" jdbcType="VARCHAR" property="updatorNo" />
    <result column="updator" jdbcType="VARCHAR" property="updator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="ys_form_num" jdbcType="VARCHAR" property="ysFormNum" />
    <result column="can_edit" jdbcType="VARCHAR" property="canEdit" />
  </resultMap>
  <sql id="Base_Column_List">
    id, po_form_num, acc_form_num, create_name, goods_name, pr_from_num, tax_rate, subject_remake, 
    currency, total_amount, retain_amount, our_party, other_party, attribution_month, 
    remark, is_valid, creator_no, creator, create_time, updator_no, updator, update_time,ys_form_num,can_edit
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bid_po_acc_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bid_po_acc_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.xhs.oa.purchasebid.model.BidPoAccInfo" useGeneratedKeys="true">
    insert into bid_po_acc_info (po_form_num, acc_form_num, create_name, 
      goods_name, pr_from_num, tax_rate, 
      subject_remake, currency, total_amount, 
      retain_amount, our_party, other_party, 
      attribution_month, remark, is_valid, 
      creator_no, creator, create_time, 
      updator_no, updator, update_time,ys_form_num,can_edit
      )
    values (#{poFormNum,jdbcType=VARCHAR}, #{accFormNum,jdbcType=VARCHAR}, #{createName,jdbcType=VARCHAR}, 
      #{goodsName,jdbcType=VARCHAR}, #{prFromNum,jdbcType=VARCHAR}, #{taxRate,jdbcType=VARCHAR}, 
      #{subjectRemake,jdbcType=VARCHAR}, #{currency,jdbcType=VARCHAR}, #{totalAmount,jdbcType=DECIMAL}, 
      #{retainAmount,jdbcType=DECIMAL}, #{ourParty,jdbcType=VARCHAR}, #{otherParty,jdbcType=VARCHAR}, 
      #{attributionMonth,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{isValid,jdbcType=TINYINT}, 
      #{creatorNo,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updatorNo,jdbcType=VARCHAR}, #{updator,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},#{ysFormNum,jdbcType=VARCHAR},#{canEdit,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.xhs.oa.purchasebid.model.BidPoAccInfo" useGeneratedKeys="true">
    insert into bid_po_acc_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="poFormNum != null">
        po_form_num,
      </if>
      <if test="accFormNum != null">
        acc_form_num,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="prFromNum != null">
        pr_from_num,
      </if>
      <if test="taxRate != null">
        tax_rate,
      </if>
      <if test="subjectRemake != null">
        subject_remake,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="retainAmount != null">
        retain_amount,
      </if>
      <if test="ourParty != null">
        our_party,
      </if>
      <if test="otherParty != null">
        other_party,
      </if>
      <if test="attributionMonth != null">
        attribution_month,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isValid != null">
        is_valid,
      </if>
      <if test="creatorNo != null">
        creator_no,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updatorNo != null">
        updator_no,
      </if>
      <if test="updator != null">
        updator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="ysFormNum != null">
        ys_form_num,
      </if>
      <if test="canEdit != null">
        can_edit,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="poFormNum != null">
        #{poFormNum,jdbcType=VARCHAR},
      </if>
      <if test="accFormNum != null">
        #{accFormNum,jdbcType=VARCHAR},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="prFromNum != null">
        #{prFromNum,jdbcType=VARCHAR},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=VARCHAR},
      </if>
      <if test="subjectRemake != null">
        #{subjectRemake,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="retainAmount != null">
        #{retainAmount,jdbcType=DECIMAL},
      </if>
      <if test="ourParty != null">
        #{ourParty,jdbcType=VARCHAR},
      </if>
      <if test="otherParty != null">
        #{otherParty,jdbcType=VARCHAR},
      </if>
      <if test="attributionMonth != null">
        #{attributionMonth,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isValid != null">
        #{isValid,jdbcType=TINYINT},
      </if>
      <if test="creatorNo != null">
        #{creatorNo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorNo != null">
        #{updatorNo,jdbcType=VARCHAR},
      </if>
      <if test="updator != null">
        #{updator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ysFormNum != null">
        #{ysFormNum,jdbcType=VARCHAR},
      </if>
      <if test="canEdit != null">
        #{canEdit,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhs.oa.purchasebid.model.BidPoAccInfo">
    update bid_po_acc_info
    <set>
      <if test="poFormNum != null">
        po_form_num = #{poFormNum,jdbcType=VARCHAR},
      </if>
      <if test="accFormNum != null">
        acc_form_num = #{accFormNum,jdbcType=VARCHAR},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="prFromNum != null">
        pr_from_num = #{prFromNum,jdbcType=VARCHAR},
      </if>
      <if test="taxRate != null">
        tax_rate = #{taxRate,jdbcType=VARCHAR},
      </if>
      <if test="subjectRemake != null">
        subject_remake = #{subjectRemake,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="retainAmount != null">
        retain_amount = #{retainAmount,jdbcType=DECIMAL},
      </if>
      <if test="ourParty != null">
        our_party = #{ourParty,jdbcType=VARCHAR},
      </if>
      <if test="otherParty != null">
        other_party = #{otherParty,jdbcType=VARCHAR},
      </if>
      <if test="attributionMonth != null">
        attribution_month = #{attributionMonth,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isValid != null">
        is_valid = #{isValid,jdbcType=TINYINT},
      </if>
      <if test="creatorNo != null">
        creator_no = #{creatorNo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorNo != null">
        updator_no = #{updatorNo,jdbcType=VARCHAR},
      </if>
      <if test="updator != null">
        updator = #{updator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ysFormNum != null">
        ys_form_num =#{ysFormNum,jdbcType=VARCHAR},
      </if>
      <if test="canEdit != null">
        can_edit =#{canEdit,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidPoAccInfo">
    update bid_po_acc_info
    set po_form_num = #{poFormNum,jdbcType=VARCHAR},
      acc_form_num = #{accFormNum,jdbcType=VARCHAR},
      create_name = #{createName,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      pr_from_num = #{prFromNum,jdbcType=VARCHAR},
      tax_rate = #{taxRate,jdbcType=VARCHAR},
      subject_remake = #{subjectRemake,jdbcType=VARCHAR},
      currency = #{currency,jdbcType=VARCHAR},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      retain_amount = #{retainAmount,jdbcType=DECIMAL},
      our_party = #{ourParty,jdbcType=VARCHAR},
      other_party = #{otherParty,jdbcType=VARCHAR},
      attribution_month = #{attributionMonth,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_valid = #{isValid,jdbcType=TINYINT},
      creator_no = #{creatorNo,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      updator_no = #{updatorNo,jdbcType=VARCHAR},
      updator = #{updator,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      ys_form_num = #{ysFormNum,jdbcType=VARCHAR},
      can_edit = #{canEdit,jdbcType=VARCHAR},
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryAccInfoByPoFormNum" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bid_po_acc_info where is_valid =1 and po_form_num = #{poFormNum} and can_edit = '0'

  </select>
</mapper>