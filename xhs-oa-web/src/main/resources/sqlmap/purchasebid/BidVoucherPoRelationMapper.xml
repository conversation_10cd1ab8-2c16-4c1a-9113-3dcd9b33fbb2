<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.BidVoucherPoRelationMapper" > 

	<sql id="BaseColumn">
          id     id,
          batch_voucher_id     batchVoucherId,
          order_form_num     orderFormNum,
          payment_form_num     paymentFormNum,
          share_amount     shareAmount,
          creator_no     creatorNo,
          creator     creator,
          create_time     createTime,
          update_time     updateTime
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.purchasebid.model.BidVoucherPoRelation">
	    select 
	    <include refid="BaseColumn"/>
	    from bid_voucher_po_relation
	    where id = #{id}
	</select>

    <select id="selectByBatchId" resultType="com.xhs.oa.purchasebid.model.BidVoucherPoRelation">
        select <include refid="BaseColumn"/>
        from bid_voucher_po_relation
        where batch_voucher_id=#{batchId}
    </select>

    <select id="findVoucherPoInfos" resultType="com.xhs.oa.purchasebid.dto.VoucherPoDto">
        select
          a.order_form_num orderFormNum,
          b.invoice_difference invoiceDifference,
          a.share_amount shareAmount
        from bid_voucher_po_relation a
        join bid_purchase_order b on a.order_form_num=b.form_num
        where a.batch_voucher_id=#{batchId}
    </select>

    <select id="findPoShareAmounts" resultType="com.xhs.oa.purchasebid.dto.VoucherPoAmountDto">
        select
          bvpr.order_form_num  formNum,
          sum(bv.amount)  amount
        from bid_voucher_po_relation bvpr
        join bid_batch_voucher bbv on bvpr.batch_voucher_id=bbv.id and bbv.is_valid=1
        join bid_voucher bv on bv.batch_voucher_id=bbv.id
        and bv.audit_status=#{auditStatus} and bvpr.order_form_num in
        <foreach collection="formNums" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        group by bvpr.order_form_num
    </select>

    <select id="findPoFormNumByBatchIds" resultType="com.xhs.oa.purchasebid.model.BidVoucherPoRelation">
        select
          batch_voucher_id  batchVoucherId,
          order_form_num  orderFormNum
        from bid_voucher_po_relation
        where batch_voucher_id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryOrderInvoiceSum" resultType="java.math.BigDecimal">
        select COALESCE(sum(bv.amount), 0)
        from bid_batch_voucher bbv
        join bid_voucher bv on bv.batch_voucher_id=bbv.id
        join bid_voucher_po_relation bvpr on bvpr.batch_voucher_id=bbv.id
        where bvpr.order_form_num=#{formNum}
            and bv.audit_status in
            <foreach collection="auditStatusList" item="status" separator="," open="(" close=")">
                #{status}
            </foreach>
    </select>

    <select id="queryByPaymentFormNum" resultType="com.xhs.oa.purchasebid.model.BidVoucherPoRelation">
        select
            bvpr.id     id,
            bvpr.batch_voucher_id     batchVoucherId,
            bvpr.order_form_num     orderFormNum,
            bvpr.payment_form_num     paymentFormNum,
            bvpr.share_amount     shareAmount,
            bvpr.creator_no     creatorNo,
            bvpr.creator     creator,
            bvpr.create_time     createTime,
            bvpr.update_time     updateTime
        from bid_voucher_po_relation bvpr
                 join bid_batch_voucher bbv on bvpr.batch_voucher_id = bbv.id and bbv.is_valid = 1
        where bvpr.payment_form_num = #{paymentFormNum}
            limit 1
    </select>

    <select id="selectVoucherByFormNumForTaxCenter"
            resultType="com.xhs.oa.purchasebid.model.BidVoucherPoRelation">
        select
            bvpr.id     id,
            bvpr.batch_voucher_id     batchVoucherId,
            bvpr.order_form_num     orderFormNum,
            bvpr.payment_form_num     paymentFormNum,
            bvpr.share_amount     shareAmount,
            bvpr.creator_no     creatorNo,
            bvpr.creator     creator,
            bvpr.create_time     createTime,
            bvpr.update_time     updateTime
        from bid_voucher_po_relation bvpr
                 join bid_batch_voucher bbv on bvpr.batch_voucher_id = bbv.id and bbv.is_valid = 1 and bbv.audit_status = 'audit_pass'
        where bvpr.payment_form_num = #{formNum}
            limit 1
    </select>


    <insert id="insert" parameterType="com.xhs.oa.purchasebid.model.BidVoucherPoRelation">
        insert into bid_voucher_po_relation (
            batch_voucher_id,
            order_form_num,
            payment_form_num,
            share_amount,
            creator_no,
            creator,
            create_time,
            update_time
        )
        values (
                   #{batchVoucherId},
                   #{orderFormNum},
                   #{paymentFormNum},
                   #{shareAmount},
                   #{creatorNo},
                   #{creator},
                   now(),
                   now()
               )
  </insert>

    <insert id="batchInsert">
        insert into bid_voucher_po_relation (
          batch_voucher_id,
          order_form_num,
          share_amount,
          creator_no,
          creator,
          create_time,
          update_time
      )
    values
    <foreach collection="list" item="item" separator=",">
    (
           #{item.batchVoucherId},
           #{item.orderFormNum},
           #{item.shareAmount},
           #{item.creatorNo},
           #{item.creator},
           now(),
           now()
      )
    </foreach>
    </insert>


    <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidVoucherPoRelation">
    update bid_voucher_po_relation
    set 
           id = #{id},

           batch_voucher_id = #{batchVoucherId},

           order_form_num = #{orderFormNum},

           share_amount = #{shareAmount},

           creator_no = #{creatorNo},

           creator = #{creator},

           create_time = #{createTime},

         update_time = #{updateTime}
    where id = #{id}
  </update>

    <update id="updateVoucherStatus">
        UPDATE bid_voucher bv
        JOIN bid_voucher_po_relation bvpr ON bvpr.batch_voucher_id = bv.batch_voucher_id
        JOIN bid_batch_voucher bbv ON bv.batch_voucher_id = bbv.id
        SET bv.audit_status = #{auditStatus}
        WHERE bvpr.payment_form_num = #{formNum}
    </update>

</mapper>   
