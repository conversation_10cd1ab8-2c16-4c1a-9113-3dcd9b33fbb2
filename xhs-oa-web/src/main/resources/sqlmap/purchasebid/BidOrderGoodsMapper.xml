<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.BidOrderGoodsMapper" > 

	<sql id="BaseColumn">
          id     id,
          purchase_order_id     purchaseOrderId,
          goods_name     goodsName,
          specification  specification,
          goods_type     goodsType,
          origin_unit_price     originUnitPrice,
          rmb_unit_price     rmbUnitPrice,
          purchase_amount     purchaseAmount,
          actual_origin_unit_price  actualOriginUnitPrice,
          actual_delivery_amount     actualDeliveryAmount,
          actual_delivery_price  actualDeliveryPrice,
          unit  unit,
          is_valid     isValid,
          creator_no     creator<PERSON><PERSON>,
          creator     creator,
          create_time     createTime,
          update_time     updateTime
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.purchasebid.model.BidOrderGoods">
	    select 
	    <include refid="BaseColumn"/>
	    from bid_order_goods
	    where id = #{id}
	</select>

    <select id="selectByOrderId" resultType="com.xhs.oa.purchasebid.model.BidOrderGoods">
        select
        <include refid="BaseColumn"/>
        from bid_order_goods
        where is_valid=1 and purchase_order_id=#{orderId}
    </select>

    <insert id="batchInsert">
        insert into bid_order_goods (
          purchase_order_id,
          goods_name,
          specification,
          goods_type,
          origin_unit_price,
          rmb_unit_price,
          purchase_amount,
          actual_origin_unit_price,
          actual_delivery_amount,
          actual_delivery_price,
          unit,
          is_valid,
          creator_no,
          creator,
          create_time,
          update_time
      )
    values
    <foreach collection="list" item="item" separator=",">
    (
           #{item.purchaseOrderId},
           #{item.goodsName},
           #{item.specification},
           #{item.goodsType},
           #{item.originUnitPrice},
           #{item.rmbUnitPrice},
           #{item.purchaseAmount},
           #{item.actualOriginUnitPrice},
           #{item.actualDeliveryAmount},
           #{item.actualDeliveryPrice},
           #{item.unit},
           1,
           #{item.creatorNo},
           #{item.creator},
           now(),
           now()
      )
    </foreach>
    </insert>

    <update id="invalidateByOrderId">
        update bid_order_goods
        set is_valid=0, update_time=now()
        where purchase_order_id=#{orderId}
    </update>

    <update id="batchUpdateActualDelivery">
        <foreach collection="list" item="item" separator=";">
            update bid_order_goods
            set actual_delivery_price=#{item.actualDeliveryPrice},
                actual_delivery_amount=#{item.actualDeliveryAmount},
                actual_origin_unit_price=#{item.actualOriginUnitPrice},
                update_time=now()
            where id=#{item.id}
        </foreach>
    </update>

    <update id="batchUpdateGoods">
        <foreach collection="list" item="item" separator=";">
            update bid_order_goods
            set
              specification=#{item.specification},
              goods_type=#{item.goodsType},
              origin_unit_price=#{item.originUnitPrice},
              rmb_unit_price=#{item.rmbUnitPrice},
              purchase_amount=#{item.purchaseAmount},
              unit=#{item.unit},
              update_time=now()
            where id=#{item.id}
        </foreach>
    </update>

    <update id="batchInvalidate">
        update bid_order_goods
        set is_valid=0, update_time=now()
        where id in
        <foreach collection="list" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>

</mapper>   
