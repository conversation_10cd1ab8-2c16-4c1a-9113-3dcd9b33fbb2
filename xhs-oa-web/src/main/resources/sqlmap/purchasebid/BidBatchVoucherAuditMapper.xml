<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.BidBatchVoucherAuditMapper" > 

	<sql id="BaseColumn">
          id     id,
          batch_voucher_id     batchVoucherId,
          audit_result     auditResult,
          remark  remark,
          creator_no     creator<PERSON><PERSON>,
          creator     creator,
          create_time     createTime
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.purchasebid.model.BidBatchVoucherAudit">
	    select 
	    <include refid="BaseColumn"/>
	    from bid_batch_voucher_audit
	    where id = #{id}
	</select>

    <select id="selectByRefIdAndAuditType" resultType="com.xhs.oa.purchasebid.model.BidBatchVoucherAudit">
        select <include refid="BaseColumn"/>
        from bid_batch_voucher_audit
        where (batch_voucher_id=#{paramsMap.batchRefId} and audit_type=#{paramsMap.batchRefType})
        or (audit_type=#{paramsMap.singleRefType}
        and batch_voucher_id in <foreach collection="paramsMap.singleRefIds" item="singleRefId" separator="," open="(" close=")">
        #{singleRefId}
        </foreach>)
    </select>


    <insert id="insert" parameterType="com.xhs.oa.purchasebid.model.BidBatchVoucherAudit">
    insert into bid_batch_voucher_audit (
          batch_voucher_id,
          audit_result,
          remark,
          creator_no,
          creator,
          create_time
      )
    values (
           #{batchVoucherId},
           #{auditResult},
           #{remark},
           #{creatorNo},
           #{creator},
           now()
      )
  </insert>

    <insert id="batchInsert">
        insert into bid_batch_voucher_audit (
          batch_voucher_id,
          audit_type,
          audit_result,
          remark,
          creator_no,
          creator,
          create_time
      )
    values
    <foreach collection="list" item="item" separator=",">
        (
        #{item.batchVoucherId},
        #{item.auditType},
        #{item.auditResult},
        #{item.remark},
        #{item.creatorNo},
        #{item.creator},
        now()
        )
    </foreach>
    </insert>


    <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidBatchVoucherAudit">
    update bid_batch_voucher_audit
    set 
           id = #{id},

           batch_voucher_id = #{batchVoucherId},

           audit_result = #{auditResult},

           creator_no = #{creatorNo},

           creator = #{creator},

         create_time = #{createTime}
    where id = #{id}
  </update>

</mapper>   
