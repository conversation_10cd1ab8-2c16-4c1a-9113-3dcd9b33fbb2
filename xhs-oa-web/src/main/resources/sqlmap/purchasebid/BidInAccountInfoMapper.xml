<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhs.oa.purchasebid.mapper.BidInAccountInfoMapper">
  <resultMap id="BaseResultMap" type="com.xhs.oa.purchasebid.model.BidInAccountInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="form_num" jdbcType="VARCHAR" property="formNum" />
    <result column="pr_form_num" jdbcType="VARCHAR" property="prFormNum" />
    <result column="our_company" jdbcType="VARCHAR" property="ourCompany" />
    <result column="our_company_code"  jdbcType="VARCHAR" property="ourCompanyCode" />
    <result column="pr_goods_id" jdbcType="BIGINT" property="prGoodsId" />
    <result column="pr_goods_name" jdbcType="VARCHAR" property="prGoodsName" />
    <result column="is_cost_cycle" jdbcType="TINYINT" property="isCostCycle" />
    <result column="is_contain_secular_property" jdbcType="TINYINT" property="isContainSecularProperty" />
    <result column="is_to_stall_cost" jdbcType="TINYINT" property="isToStallCost" />
    <result column="indicated_content" jdbcType="TINYINT" property="indicatedContent" />
    <result column="eas_dept_name" jdbcType="VARCHAR" property="easDeptName" />
    <result column="eas_dept_name_code" jdbcType="VARCHAR" property="easDeptNameCode" />
    <result column="eas_cost_subject" jdbcType="VARCHAR" property="easCostSubject" />
    <result column="eas_cost_subject_code" jdbcType="VARCHAR" property="easCostSubjectCode" />
    <result column="eas_withholding_subject" jdbcType="VARCHAR" property="easWithholdingSubject" />
    <result column="eas_withholding_subject_code" jdbcType="VARCHAR" property="easWithholdingSubjectCode" />
    <result column="eas_business" jdbcType="VARCHAR" property="easBusiness" />
    <result column="eas_business_code" jdbcType="VARCHAR" property="easBusinessCode" />
    <result column="eas_supplier_no" jdbcType="VARCHAR" property="easSupplierNo" />
    <result column="in_account_type" jdbcType="TINYINT" property="inAccountType" />
    <result column="is_valid" jdbcType="TINYINT" property="isValid" />
    <result column="creator_no" jdbcType="VARCHAR" property="creatorNo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updator_no" jdbcType="VARCHAR" property="updatorNo"/>
    <result column="updator" jdbcType="VARCHAR" property="updator"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="in_account_amount" jdbcType="DECIMAL" property="inAccountAmount"/>
    <result column="ys_form_num" jdbcType="VARCHAR" property="ysFormNum"/>
    <result column="vat" jdbcType="VARCHAR" property="vat"/>
    <result column="currency" jdbcType="VARCHAR" property="currency"/>
    <result column="can_edit" jdbcType="VARCHAR" property="canEdit"/>
    <result column="goods_amount" jdbcType="DECIMAL" property="goodsAmount"/>
    <result column="period_time" jdbcType="VARCHAR" property="periodTime"/>
    <result column="summary_information" jdbcType="VARCHAR" property="summaryInformation"/>
  </resultMap>

  <resultMap id="BaseResultMap1" type="com.xhs.oa.purchasebid.vo.BidInAccountInfoVo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="form_num" jdbcType="VARCHAR" property="formNum" />
    <result column="pr_form_num" jdbcType="VARCHAR" property="prFormNum" />
    <result column="our_company" jdbcType="VARCHAR" property="ourCompany" />
    <result column="our_company_code"  jdbcType="VARCHAR" property="ourCompanyCode" />
    <result column="pr_goods_id" jdbcType="BIGINT" property="prGoodsId" />
    <result column="pr_goods_name" jdbcType="VARCHAR" property="prGoodsName" />
    <result column="is_cost_cycle" jdbcType="TINYINT" property="isCostCycle" />
    <result column="is_contain_secular_property" jdbcType="TINYINT" property="isContainSecularProperty" />
    <result column="is_to_stall_cost" jdbcType="TINYINT" property="isToStallCost" />
    <result column="indicated_content" jdbcType="TINYINT" property="indicatedContent" />
    <result column="eas_dept_name" jdbcType="VARCHAR" property="easDeptName" />
    <result column="eas_dept_name_code" jdbcType="VARCHAR" property="easDeptNameCode" />
    <result column="eas_cost_subject" jdbcType="VARCHAR" property="easCostSubject" />
    <result column="eas_cost_subject_code" jdbcType="VARCHAR" property="easCostSubjectCode" />
    <result column="eas_withholding_subject" jdbcType="VARCHAR" property="easWithholdingSubject" />
    <result column="eas_withholding_subject_code" jdbcType="VARCHAR" property="easWithholdingSubjectCode" />
    <result column="eas_business" jdbcType="VARCHAR" property="easBusiness"/>
    <result column="eas_business_code" jdbcType="VARCHAR" property="easBusinessCode"/>
    <result column="eas_supplier_no" jdbcType="VARCHAR" property="easSupplierNo"/>
    <result column="in_account_type" jdbcType="TINYINT" property="inAccountType"/>
    <result column="in_account_amount" jdbcType="DECIMAL" property="inAccountAmount"/>
    <result column="ys_form_num" jdbcType="VARCHAR" property="ysFormNum"/>
    <result column="vat" jdbcType="VARCHAR" property="vat"/>
    <result column="currency" jdbcType="VARCHAR" property="currency"/>
    <result column="can_edit" jdbcType="VARCHAR" property="canEdit"/>
    <result column="goods_amount" jdbcType="DECIMAL" property="goodsAmount"/>
    <result column="period_time" jdbcType="VARCHAR" property="periodTime"/>
    <result column="summary_information" jdbcType="VARCHAR" property="summaryInformation"/>
  </resultMap>
  <sql id="Base_Column_List">
    id
    , form_num, pr_form_num, our_company, our_company_code,pr_goods_id, pr_goods_name, is_cost_cycle,
    is_contain_secular_property, is_to_stall_cost, indicated_content, eas_dept_name,
    eas_dept_name_code, eas_cost_subject, eas_cost_subject_code, eas_withholding_subject,
    eas_withholding_subject_code, eas_business,eas_business_code, in_account_type, is_valid, creator_no,
    creator, create_time, updator_no, updator, update_time, in_account_amount,eas_supplier_no,ys_form_num,vat,currency,can_edit,goods_amount,period_time,summary_information
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bid_in_account_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bid_in_account_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.xhs.oa.purchasebid.model.BidInAccountInfo" useGeneratedKeys="true">
    insert into bid_in_account_info (form_num, pr_form_num, our_company, our_company_code,
                                     pr_goods_id, pr_goods_name, is_cost_cycle,
                                     is_contain_secular_property, is_to_stall_cost,
                                     indicated_content, eas_dept_name, eas_dept_name_code,
                                     eas_cost_subject, eas_cost_subject_code, eas_withholding_subject,
                                     eas_withholding_subject_code, eas_business, eas_business_code,
                                     in_account_type, is_valid, creator_no,
                                     creator, create_time, updator_no,
                                     updator, update_time, in_account_amount, eas_supplier_no, ys_form_num, vat,
                                     currency, can_edit, goods_amount, period_time, summary_information)
    values (#{formNum,jdbcType=VARCHAR}, #{prFormNum,jdbcType=VARCHAR}, #{ourCompany,jdbcType=VARCHAR},
            #{ourCompanyCode,jdbcType=VARCHAR},
            #{prGoodsId,jdbcType=BIGINT}, #{prGoodsName,jdbcType=VARCHAR}, #{isCostCycle,jdbcType=TINYINT},
            #{isContainSecularProperty,jdbcType=TINYINT}, #{isToStallCost,jdbcType=TINYINT},
            #{indicatedContent,jdbcType=TINYINT}, #{easDeptName,jdbcType=VARCHAR}, #{easDeptNameCode,jdbcType=VARCHAR},
            #{easCostSubject,jdbcType=VARCHAR}, #{easCostSubjectCode,jdbcType=VARCHAR},
            #{easWithholdingSubject,jdbcType=VARCHAR},
            #{easWithholdingSubjectCode,jdbcType=VARCHAR}, #{easBusiness,jdbcType=VARCHAR},
            #{easBusinessCode,jdbcType=VARCHAR},
            #{inAccountType,jdbcType=TINYINT}, #{isValid,jdbcType=TINYINT}, #{creatorNo,jdbcType=VARCHAR},
            #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updatorNo,jdbcType=VARCHAR},
            #{updator,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{inAccountAmount,jdbcType=DECIMAL},
            #{easSupplierNo,jdbcType=VARCHAR},
            #{ysFormNum,jdbcType=VARCHAR}, #{vat,jdbcType=VARCHAR}, #{currency,jdbcType=VARCHAR},
            #{canEdit,jdbcType=VARCHAR}, #{goodsAmount,jdbcType=DECIMAL},
            #{periodTime,jdbcType=VARCHAR}, #{summaryInformation,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.xhs.oa.purchasebid.model.BidInAccountInfo" useGeneratedKeys="true">
    insert into bid_in_account_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="formNum != null">
        form_num,
      </if>
      <if test="prFormNum != null">
        pr_form_num,
      </if>
      <if test="ourCompany != null">
        our_company,
      </if>
      <if test="ourCompanyCode != null">
        our_company_code,
      </if>
      <if test="prGoodsId != null">
        pr_goods_id,
      </if>
      <if test="prGoodsName != null">
        pr_goods_name,
      </if>
      <if test="isCostCycle != null">
        is_cost_cycle,
      </if>
      <if test="isContainSecularProperty != null">
        is_contain_secular_property,
      </if>
      <if test="isToStallCost != null">
        is_to_stall_cost,
      </if>
      <if test="indicatedContent != null">
        indicated_content,
      </if>
      <if test="easDeptName != null">
        eas_dept_name,
      </if>
      <if test="easDeptNameCode != null">
        eas_dept_name_code,
      </if>
      <if test="easCostSubject != null">
        eas_cost_subject,
      </if>
      <if test="easCostSubjectCode != null">
        eas_cost_subject_code,
      </if>
      <if test="easWithholdingSubject != null">
        eas_withholding_subject,
      </if>
      <if test="easWithholdingSubjectCode != null">
        eas_withholding_subject_code,
      </if>
      <if test="easBusiness != null">
        eas_business,
      </if>
      <if test="easBusinessCode != null">
        eas_business_code,
      </if>
      <if test="inAccountType != null">
        in_account_type,
      </if>
      <if test="isValid != null">
        is_valid,
      </if>
      <if test="creatorNo != null">
        creator_no,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updatorNo != null">
        updator_no,
      </if>
      <if test="updator != null">
        updator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="inAccountAmount != null">
        in_account_amount,
      </if>
      <if test="easSupplierNo != null">
        eas_supplier_no,
      </if>

      <if test="ysFormNum != null">
        ys_form_num,
      </if>
      <if test="vat != null">
        vat,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="canEdit != null">
        can_edit,
      </if>
      <if test="goodsAmount != null">
        goods_amount,
      </if>
      <if test="periodTime != null">
        period_time,
      </if>
      <if test="summaryInformation != null">
        summary_information,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="formNum != null">
        #{formNum,jdbcType=VARCHAR},
      </if>
      <if test="prFormNum != null">
        #{prFormNum,jdbcType=VARCHAR},
      </if>
      <if test="ourCompany != null">
        #{ourCompany,jdbcType=VARCHAR},
      </if>
      <if test="ourCompanyCode != null">
        #{ourCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="prGoodsId != null">
        #{prGoodsId,jdbcType=BIGINT},
      </if>
      <if test="prGoodsName != null">
        #{prGoodsName,jdbcType=VARCHAR},
      </if>
      <if test="isCostCycle != null">
        #{isCostCycle,jdbcType=TINYINT},
      </if>
      <if test="isContainSecularProperty != null">
        #{isContainSecularProperty,jdbcType=TINYINT},
      </if>
      <if test="isToStallCost != null">
        #{isToStallCost,jdbcType=TINYINT},
      </if>
      <if test="indicatedContent != null">
        #{indicatedContent,jdbcType=TINYINT},
      </if>
      <if test="easDeptName != null">
        #{easDeptName,jdbcType=VARCHAR},
      </if>
      <if test="easDeptNameCode != null">
        #{easDeptNameCode,jdbcType=VARCHAR},
      </if>
      <if test="easCostSubject != null">
        #{easCostSubject,jdbcType=VARCHAR},
      </if>
      <if test="easCostSubjectCode != null">
        #{easCostSubjectCode,jdbcType=VARCHAR},
      </if>
      <if test="easWithholdingSubject != null">
        #{easWithholdingSubject,jdbcType=VARCHAR},
      </if>
      <if test="easWithholdingSubjectCode != null">
        #{easWithholdingSubjectCode,jdbcType=VARCHAR},
      </if>
      <if test="easBusiness != null">
        #{easBusiness,jdbcType=VARCHAR},
      </if>
      <if test="easBusinessCode != null">
        #{easBusinessCode,jdbcType=VARCHAR},
      </if>
      <if test="inAccountType != null">
        #{inAccountType,jdbcType=TINYINT},
      </if>
      <if test="isValid != null">
        #{isValid,jdbcType=TINYINT},
      </if>
      <if test="creatorNo != null">
        #{creatorNo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorNo != null">
        #{updatorNo,jdbcType=VARCHAR},
      </if>
      <if test="updator != null">
        #{updator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="inAccountAmount != null">
        #{inAccountAmount,jdbcType=DECIMAL},
      </if>
      <if test="easSupplierNo != null">
        #{easSupplierNo,jdbcType=VARCHAR},
      </if>
      <if test="ysFormNum != null">
        #{ysFormNum,jdbcType=VARCHAR},
      </if>
      <if test="vat != null">
        #{vat,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="canEdit != null">
        #{canEdit,jdbcType=VARCHAR},
      </if>
      <if test="goodsAmount != null">
        #{goodsAmount,jdbcType=DECIMAL},
      </if>
      <if test="periodTime != null">
        #{periodTime,jdbcType=VARCHAR},
      </if>
      <if test="summaryInformation != null">
        #{summaryInformation,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhs.oa.purchasebid.model.BidInAccountInfo">
    update bid_in_account_info
    <set>
      <if test="formNum != null">
        form_num = #{formNum,jdbcType=VARCHAR},
      </if>
      <if test="prFormNum != null">
        pr_form_num = #{prFormNum,jdbcType=VARCHAR},
      </if>
      <if test="ourCompany != null">
        our_company = #{ourCompany,jdbcType=VARCHAR},
      </if>
      <if test="ourCompanyCode != null">
        our_company_code = #{ourCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="prGoodsId != null">
        pr_goods_id = #{prGoodsId,jdbcType=BIGINT},
      </if>
      <if test="prGoodsName != null">
        pr_goods_name = #{prGoodsName,jdbcType=VARCHAR},
      </if>
      <if test="isCostCycle != null">
        is_cost_cycle = #{isCostCycle,jdbcType=TINYINT},
      </if>
      <if test="isContainSecularProperty != null">
        is_contain_secular_property = #{isContainSecularProperty,jdbcType=TINYINT},
      </if>
      <if test="isToStallCost != null">
        is_to_stall_cost = #{isToStallCost,jdbcType=TINYINT},
      </if>
      <if test="indicatedContent != null">
        indicated_content = #{indicatedContent,jdbcType=TINYINT},
      </if>
      <if test="easDeptName != null">
        eas_dept_name = #{easDeptName,jdbcType=VARCHAR},
      </if>
      <if test="easDeptNameCode != null">
        eas_dept_name_code = #{easDeptNameCode,jdbcType=VARCHAR},
      </if>
      <if test="easCostSubject != null">
        eas_cost_subject = #{easCostSubject,jdbcType=VARCHAR},
      </if>
      <if test="easCostSubjectCode != null">
        eas_cost_subject_code = #{easCostSubjectCode,jdbcType=VARCHAR},
      </if>
      <if test="easWithholdingSubject != null">
        eas_withholding_subject = #{easWithholdingSubject,jdbcType=VARCHAR},
      </if>
      <if test="easWithholdingSubjectCode != null">
        eas_withholding_subject_code = #{easWithholdingSubjectCode,jdbcType=VARCHAR},
      </if>
      <if test="easBusiness != null">
        eas_business = #{easBusiness,jdbcType=VARCHAR},
      </if>
      <if test="easBusinessCode != null">
        eas_business_code = #{easBusinessCode,jdbcType=VARCHAR},
      </if>
      <if test="inAccountType != null">
        in_account_type = #{inAccountType,jdbcType=TINYINT},
      </if>
      <if test="isValid != null">
        is_valid = #{isValid,jdbcType=TINYINT},
      </if>
      <if test="creatorNo != null">
        creator_no = #{creatorNo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorNo != null">
        updator_no = #{updatorNo,jdbcType=VARCHAR},
      </if>
      <if test="updator != null">
        updator = #{updator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="inAccountAmount != null">
        in_account_amount = #{inAccountAmount,jdbcType=DECIMAL},
      </if>
      <if test="easSupplierNo != null">
        eas_supplier_no = #{easSupplierNo,jdbcType=VARCHAR},
      </if>

      <if test="ysFormNum != null">
        ys_form_num = #{ysFormNum,jdbcType=VARCHAR},
      </if>
      <if test="vat != null">
        vat = #{vat,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="canEdit != null">
        can_edit = #{canEdit,jdbcType=VARCHAR},
      </if>
      <if test="goodsAmount != null">
        goods_amount =#{goodsAmount,jdbcType=DECIMAL},
      </if>
      <if test="periodTime != null">
        period_time =#{periodTime,jdbcType=VARCHAR},
      </if>
      <if test="summaryInformation != null">
        summary_information =#{summaryInformation,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>


  <update id="updateById" parameterType="com.xhs.oa.purchasebid.vo.BidInAccountInfoVo">
    update bid_in_account_info
    <set>
      <if test="formNum != null">
        form_num = #{formNum,jdbcType=VARCHAR},
      </if>
      <if test="prFormNum != null">
        pr_form_num = #{prFormNum,jdbcType=VARCHAR},
      </if>
      <if test="ourCompany != null">
        our_company = #{ourCompany,jdbcType=VARCHAR},
      </if>
      <if test="ourCompanyCode != null">
        our_company_code = #{ourCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="prGoodsId != null">
        pr_goods_id = #{prGoodsId,jdbcType=BIGINT},
      </if>
      <if test="prGoodsName != null">
        pr_goods_name = #{prGoodsName,jdbcType=VARCHAR},
      </if>
      <if test="isCostCycle != null">
        is_cost_cycle = #{isCostCycle,jdbcType=TINYINT},
      </if>
      <if test="isContainSecularProperty != null">
        is_contain_secular_property = #{isContainSecularProperty,jdbcType=TINYINT},
      </if>
      <if test="isToStallCost != null">
        is_to_stall_cost = #{isToStallCost,jdbcType=TINYINT},
      </if>
      <if test="indicatedContent != null">
        indicated_content = #{indicatedContent,jdbcType=TINYINT},
      </if>
      <if test="easDeptName != null">
        eas_dept_name = #{easDeptName,jdbcType=VARCHAR},
      </if>
      <if test="easDeptNameCode != null">
        eas_dept_name_code = #{easDeptNameCode,jdbcType=VARCHAR},
      </if>
      <if test="easCostSubject != null">
        eas_cost_subject = #{easCostSubject,jdbcType=VARCHAR},
      </if>
      <if test="easCostSubjectCode != null">
        eas_cost_subject_code = #{easCostSubjectCode,jdbcType=VARCHAR},
      </if>
      <if test="easWithholdingSubject != null">
        eas_withholding_subject = #{easWithholdingSubject,jdbcType=VARCHAR},
      </if>
      <if test="easWithholdingSubjectCode != null">
        eas_withholding_subject_code = #{easWithholdingSubjectCode,jdbcType=VARCHAR},
      </if>
      <if test="easBusiness != null">
        eas_business = #{easBusiness,jdbcType=VARCHAR},
      </if>
      <if test="easBusinessCode != null">
        eas_business_code = #{easBusinessCode,jdbcType=VARCHAR},
      </if>
      <if test="inAccountType != null">
        in_account_type = #{inAccountType,jdbcType=TINYINT},
      </if>
      <if test="isValid != null">
        is_valid = #{isValid,jdbcType=TINYINT},
      </if>
      <if test="creatorNo != null">
        creator_no = #{creatorNo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorNo != null">
        updator_no = #{updatorNo,jdbcType=VARCHAR},
      </if>
      <if test="updator != null">
        updator = #{updator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="inAccountAmount != null">
        in_account_amount = #{inAccountAmount,jdbcType=DECIMAL},
      </if>
      <if test="easSupplierNo != null">
        eas_supplier_no = #{easSupplierNo,jdbcType=VARCHAR},
      </if>
      <if test="ysFormNum != null">
        ys_form_num = #{ysFormNum,jdbcType=VARCHAR},
      </if>
      <if test="vat != null">
        vat = #{vat,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="canEdit != null">
        can_edit = #{canEdit,jdbcType=VARCHAR},
      </if>
      <if test="goodsAmount != null">
        goods_amount =#{goodsAmount,jdbcType=DECIMAL},
      </if>
      <if test="periodTime != null">
        period_time =#{periodTime,jdbcType=VARCHAR},
      </if>
      <if test="summaryInformation != null">
        summary_information =#{summaryInformation,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>


  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidInAccountInfo">
    update bid_in_account_info
    set form_num = #{formNum,jdbcType=VARCHAR},
        pr_form_num = #{prFormNum,jdbcType=VARCHAR},
        our_company = #{ourCompany,jdbcType=VARCHAR},
        our_company_code = #{ourCompanyCode,jdbcType=VARCHAR},
        pr_goods_id = #{prGoodsId,jdbcType=BIGINT},
        pr_goods_name = #{prGoodsName,jdbcType=VARCHAR},
        is_cost_cycle = #{isCostCycle,jdbcType=TINYINT},
        is_contain_secular_property = #{isContainSecularProperty,jdbcType=TINYINT},
        is_to_stall_cost = #{isToStallCost,jdbcType=TINYINT},
        indicated_content = #{indicatedContent,jdbcType=TINYINT},
        eas_dept_name = #{easDeptName,jdbcType=VARCHAR},
        eas_dept_name_code = #{easDeptNameCode,jdbcType=VARCHAR},
        eas_cost_subject             = #{easCostSubject,jdbcType=VARCHAR},
        eas_cost_subject_code        = #{easCostSubjectCode,jdbcType=VARCHAR},
        eas_withholding_subject      = #{easWithholdingSubject,jdbcType=VARCHAR},
        eas_withholding_subject_code = #{easWithholdingSubjectCode,jdbcType=VARCHAR},
        eas_business                 = #{easBusiness,jdbcType=VARCHAR},
        eas_business_code            = #{easBusinessCode,jdbcType=VARCHAR},
        in_account_type              = #{inAccountType,jdbcType=TINYINT},
        is_valid                     = #{isValid,jdbcType=TINYINT},
        creator_no                   = #{creatorNo,jdbcType=VARCHAR},
        creator                      = #{creator,jdbcType=VARCHAR},
        create_time                  = #{createTime,jdbcType=TIMESTAMP},
        updator_no                   = #{updatorNo,jdbcType=VARCHAR},
        updator                      = #{updator,jdbcType=VARCHAR},
        update_time                  = #{updateTime,jdbcType=TIMESTAMP},
        in_account_amount            = #{inAccountAmount,jdbcType=DECIMAL},
        eas_supplier_no              = #{easSupplierNo,jdbcType=VARCHAR},
        ys_form_num                  = #{ysFormNum,jdbcType=VARCHAR},
        vat                          = #{vat,jdbcType=VARCHAR},
        currency                     = #{currency,jdbcType=VARCHAR},
        can_edit                     = #{canEdit,jdbcType=VARCHAR},
        goods_amount                 =#{goodsAmount,jdbcType=DECIMAL},
        period_time                  =#{periodTime,jdbcType=VARCHAR},
        summary_information          =#{summaryInformation,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByFormNum" parameterType="string" resultMap="BaseResultMap1">
    select
    <include refid="Base_Column_List" />
    from bid_in_account_info
    where form_num = #{formNum} and is_valid = 1
  </select>

  <delete id="deleteByFormNum" parameterType="java.lang.String">
    delete from bid_in_account_info
    where form_num = #{prFormNum,jdbcType=VARCHAR}
  </delete>

  <select id="queryPoInAccountInfoList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bid_in_account_info
    where form_num = #{poFormNum} and in_account_type = 1 and is_valid = 1

  </select>

  <update id="isValidInAccountInfo">
    update bid_in_account_info set is_valid = 0  where form_num = #{formNum} and can_edit = '1'
  </update>

  <select id="queryInAccountInfoByIds" resultMap="BaseResultMap" parameterType="list">
    select
    <include refid="Base_Column_List" />
    from bid_in_account_info
    where id in
    <foreach collection="list" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="queryPoInAccountInfoListByYsFormNum" parameterType="string" resultMap="BaseResultMap1">
    select
    <include refid="Base_Column_List" />
    from bid_in_account_info
    where  ys_form_num= #{ysFormNum} and in_account_type = 1 and is_valid = 1 and can_edit = '0'

  </select>

  <select id="queryYsFormNumByFkFormNum" parameterType="string" resultType="string">
    select
      ys_form_num
    from t_payment_acceptance
    where fk_form_num = #{fkFormNum} and is_valid =1 limit 1

  </select>

</mapper>