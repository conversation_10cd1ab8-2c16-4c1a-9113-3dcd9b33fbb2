<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.BidRatingStandardTitleMapper" > 

	<sql id="BaseColumn">
          id     id,
          bidding_id     biddingId,
          standard_desc     standardDesc,
          standard_percentage     standardPercentage,
          operator_no     operatorNo,
          operator     operator,
          create_time     createTime
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.purchasebid.model.BidRatingStandardTitle">
	    select 
	    <include refid="BaseColumn"/>
	    from bid_rating_standard_title
	    where id = #{id}
	</select>

    <select id="selectByBiddingId" resultType="com.xhs.oa.purchasebid.model.BidRatingStandardTitle">
        select <include refid="BaseColumn"/>
        from bid_rating_standard_title
        where is_valid=1 and bidding_id=#{biddingId}
    </select>


    <insert id="insert" parameterType="com.xhs.oa.purchasebid.model.BidRatingStandardTitle">
    insert into bid_rating_standard_title (
          id,
          bidding_id,
          standard_desc,
          standard_percentage,
          operator_no,
          operator,
          create_time
      )
    values (
           #{id},
           #{biddingId},
           #{standardDesc},
           #{standardPercentage},
           #{operatorNo},
           #{operator},
          #{createTime}
      )
  </insert>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
      insert into bid_rating_standard_title (
          bidding_id,
          standard_desc,
          standard_percentage,
          is_valid,
          operator_no,
          operator,
          create_time
      )
      values
      <foreach collection="list" item="item" separator=",">
          (
          #{item.biddingId},
          #{item.standardDesc},
          #{item.standardPercentage},
          1,
          #{item.operatorNo},
          #{item.operator},
          now()
          )
      </foreach>
    </insert>


    <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidRatingStandardTitle">
    update bid_rating_standard_title
    set 
           id = #{id},

           bidding_id = #{biddingId},

           standard_desc = #{standardDesc},

           standard_percentage = #{standardPercentage},

           operator_no = #{operatorNo},

           operator = #{operator},

         create_time = #{createTime}
    where id = #{id}
  </update>

    <update id="invalidateByBiddingId">
        update bid_rating_standard_title
        set is_valid=0
        where bidding_id=#{biddingId}
    </update>

</mapper>   
