<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhs.oa.purchasebid.mapper.BidPoPendingInfoMapper">
  <resultMap id="BaseResultMap" type="com.xhs.oa.purchasebid.model.BidPoPendingInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="po_form_num" jdbcType="VARCHAR" property="poFormNum" />
    <result column="pr_name" jdbcType="VARCHAR" property="prName" />
    <result column="current_step" jdbcType="VARCHAR" property="currentStep" />
    <result column="po_status" jdbcType="TINYINT" property="poStatus" />
    <result column="is_valid" jdbcType="TINYINT" property="isValid" />
    <result column="po_amount" jdbcType="DECIMAL" property="poAmount" />
    <result column="our_company" jdbcType="VARCHAR" property="ourCompany" />
    <result column="counter_company" jdbcType="VARCHAR" property="counterCompany" />
    <result column="department_id_path" jdbcType="VARCHAR" property="departmentIdPath" />
    <result column="department_name_path" jdbcType="VARCHAR" property="departmentNamePath" />
    <result column="is_in_account" jdbcType="TINYINT" property="isInAccount" />
    <result column="finance_user_no" jdbcType="VARCHAR" property="financeUserNo" />
    <result column="creator_no" jdbcType="VARCHAR" property="creatorNo" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updator_no" jdbcType="VARCHAR" property="updatorNo" />
    <result column="updator" jdbcType="VARCHAR" property="updator" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="save_or_submit" jdbcType="TINYINT" property="saveOrSubmit" />
    <result column="is_input_tax" jdbcType="TINYINT" property="isInputTax"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, po_form_num, pr_name, current_step, po_status, is_valid, po_amount, our_company,
    counter_company, department_id_path, department_name_path, is_in_account, finance_user_no,
    creator_no, creator, create_time, updator_no, updator, update_time,save_or_submit,is_input_tax
  </sql>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.xhs.oa.purchasebid.model.BidPoPendingInfo" useGeneratedKeys="true">
    insert into bid_po_pending_info (po_form_num, pr_name, current_step,
                                     po_status, is_valid, po_amount,
                                     our_company, counter_company, department_id_path,
                                     department_name_path, is_in_account, finance_user_no,
                                     creator_no, creator, create_time,
                                     updator_no, updator, update_time,save_or_submit,is_input_tax
    )
    values (#{poFormNum,jdbcType=VARCHAR}, #{prName,jdbcType=VARCHAR}, #{currentStep,jdbcType=VARCHAR},
            #{poStatus,jdbcType=TINYINT}, #{isValid,jdbcType=TINYINT}, #{poAmount,jdbcType=DECIMAL},
            #{ourCompany,jdbcType=VARCHAR}, #{counterCompany,jdbcType=VARCHAR}, #{departmentIdPath,jdbcType=VARCHAR},
            #{departmentNamePath,jdbcType=VARCHAR}, #{isInAccount,jdbcType=TINYINT}, #{financeUserNo,jdbcType=VARCHAR},
            #{creatorNo,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
            #{updatorNo,jdbcType=VARCHAR}, #{updator,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},#{saveOrSubmit,jdbcType=TINYINT},#{isInputTax,jdbcType=TINYINT}
           )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.xhs.oa.purchasebid.model.BidPoPendingInfo" useGeneratedKeys="true">
    insert into bid_po_pending_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="poFormNum != null">
        po_form_num,
      </if>
      <if test="prName != null">
        pr_name,
      </if>
      <if test="currentStep != null">
        current_step,
      </if>
      <if test="poStatus != null">
        po_status,
      </if>
      <if test="isValid != null">
        is_valid,
      </if>
      <if test="poAmount != null">
        po_amount,
      </if>
      <if test="ourCompany != null">
        our_company,
      </if>
      <if test="counterCompany != null">
        counter_company,
      </if>
      <if test="departmentIdPath != null">
        department_id_path,
      </if>
      <if test="departmentNamePath != null">
        department_name_path,
      </if>
      <if test="isInAccount != null">
        is_in_account,
      </if>
      <if test="financeUserNo != null">
        finance_user_no,
      </if>
      <if test="creatorNo != null">
        creator_no,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updatorNo != null">
        updator_no,
      </if>
      <if test="updator != null">
        updator,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="saveOrSubmit != null">
        save_or_submit,
      </if>
      <if test="isInputTax != null">
        is_input_tax,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="poFormNum != null">
        #{poFormNum,jdbcType=VARCHAR},
      </if>
      <if test="prName != null">
        #{prName,jdbcType=VARCHAR},
      </if>
      <if test="currentStep != null">
        #{currentStep,jdbcType=VARCHAR},
      </if>
      <if test="poStatus != null">
        #{poStatus,jdbcType=TINYINT},
      </if>
      <if test="isValid != null">
        #{isValid,jdbcType=TINYINT},
      </if>
      <if test="poAmount != null">
        #{poAmount,jdbcType=DECIMAL},
      </if>
      <if test="ourCompany != null">
        #{ourCompany,jdbcType=VARCHAR},
      </if>
      <if test="counterCompany != null">
        #{counterCompany,jdbcType=VARCHAR},
      </if>
      <if test="departmentIdPath != null">
        #{departmentIdPath,jdbcType=VARCHAR},
      </if>
      <if test="departmentNamePath != null">
        #{departmentNamePath,jdbcType=VARCHAR},
      </if>
      <if test="isInAccount != null">
        #{isInAccount,jdbcType=TINYINT},
      </if>
      <if test="financeUserNo != null">
        #{financeUserNo,jdbcType=VARCHAR},
      </if>
      <if test="creatorNo != null">
        #{creatorNo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorNo != null">
        #{updatorNo,jdbcType=VARCHAR},
      </if>
      <if test="updator != null">
        #{updator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="saveOrSubmit != null">
        #{saveOrSubmit,jdbcType=TINYINT},
      </if>
      <if test="isInputTax != null">
        #{isInputTax,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhs.oa.purchasebid.model.BidPoPendingInfo">
    update bid_po_pending_info
    <set>
      <if test="poFormNum != null">
        po_form_num = #{poFormNum,jdbcType=VARCHAR},
      </if>
      <if test="prName != null">
        pr_name = #{prName,jdbcType=VARCHAR},
      </if>
      <if test="currentStep != null">
        current_step = #{currentStep,jdbcType=VARCHAR},
      </if>
      <if test="poStatus != null">
        po_status = #{poStatus,jdbcType=TINYINT},
      </if>
      <if test="isValid != null">
        is_valid = #{isValid,jdbcType=TINYINT},
      </if>
      <if test="poAmount != null">
        po_amount = #{poAmount,jdbcType=DECIMAL},
      </if>
      <if test="ourCompany != null">
        our_company = #{ourCompany,jdbcType=VARCHAR},
      </if>
      <if test="counterCompany != null">
        counter_company = #{counterCompany,jdbcType=VARCHAR},
      </if>
      <if test="departmentIdPath != null">
        department_id_path = #{departmentIdPath,jdbcType=VARCHAR},
      </if>
      <if test="departmentNamePath != null">
        department_name_path = #{departmentNamePath,jdbcType=VARCHAR},
      </if>
      <if test="isInAccount != null">
        is_in_account = #{isInAccount,jdbcType=TINYINT},
      </if>
      <if test="financeUserNo != null">
        finance_user_no = #{financeUserNo,jdbcType=VARCHAR},
      </if>
      <if test="creatorNo != null">
        creator_no = #{creatorNo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatorNo != null">
        updator_no = #{updatorNo,jdbcType=VARCHAR},
      </if>
      <if test="updator != null">
        updator = #{updator,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="saveOrSubmit != null">
        save_or_submit = #{saveOrSubmit,jdbcType=TINYINT},
      </if>
      <if test="isInputTax != null">
        is_input_tax = #{isInputTax,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidPoPendingInfo">
    update bid_po_pending_info
    set po_form_num = #{poFormNum,jdbcType=VARCHAR},
        pr_name = #{prName,jdbcType=VARCHAR},
        current_step = #{currentStep,jdbcType=VARCHAR},
        po_status = #{poStatus,jdbcType=TINYINT},
        is_valid = #{isValid,jdbcType=TINYINT},
        po_amount = #{poAmount,jdbcType=DECIMAL},
        our_company = #{ourCompany,jdbcType=VARCHAR},
        counter_company = #{counterCompany,jdbcType=VARCHAR},
        department_id_path = #{departmentIdPath,jdbcType=VARCHAR},
        department_name_path = #{departmentNamePath,jdbcType=VARCHAR},
        is_in_account = #{isInAccount,jdbcType=TINYINT},
        finance_user_no = #{financeUserNo,jdbcType=VARCHAR},
        creator_no = #{creatorNo,jdbcType=VARCHAR},
        creator = #{creator,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        updator_no = #{updatorNo,jdbcType=VARCHAR},
        updator = #{updator,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        save_or_submit= #{saveOrSubmit,jdbcType=TINYINT},
        is_input_tax = #{isInputTax,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bid_po_pending_info
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <select id="selectByPoFormNum" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bid_po_pending_info where po_form_num = #{poFormNum} and is_valid = 1
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bid_po_pending_info
    where id = #{id,jdbcType=BIGINT}
  </select>


  <select id="queryPoPendingInfoByCondition" parameterType="com.xhs.oa.purchasebid.param.PoPendingSearchParam" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bid_po_pending_info
    <where>
      is_valid = 1
      <if test="poFormNum != null and  poFormNum !='' ">
        and po_form_num like concat('%',#{poFormNum},'%')
      </if>
      <if test="creatorNo != null and creatorNo !=''">
        and creator_no = #{creatorNo}
      </if>
      <if test="ourCompany != null and ourCompany !=''">
        and our_company like concat('%',#{ourCompany},'%')
      </if>
      <if test="counterCompany != null and counterCompany !=''">
        and counter_company like concat('%',#{counterCompany},'%')
      </if>
      <if test="departmentIdPath != null and departmentIdPath !='' ">
        and department_id_path like concat('%',#{departmentIdPath},'%')
      </if>
      <if test="financeUserNo != null and financeUserNo !=''">
        and finance_user_no like concat('%',#{financeUserNo},'%')
      </if>
      <if test="lastUpdateNo != null and lastUpdateNo !=''">
        and updator_no = #{lastUpdateNo}
      </if>
      <if test="poStatus != null">
        and po_status = #{poStatus}
      </if>
    </where>
    order by create_time desc
    limit #{start}, #{pageSize}
  </select>

  <select id="queryPoPendingInfoCountByCondition" parameterType="com.xhs.oa.purchasebid.param.PoPendingSearchParam" resultType="int">
    select
    count(*)
    from bid_po_pending_info
    <where>
      is_valid = 1
      <if test="poFormNum != null and  poFormNum !='' ">
        and po_form_num like concat('%',#{poFormNum},'%')
      </if>
      <if test="creatorNo != null and creatorNo !=''">
        and creator_no = #{creatorNo}
      </if>
      <if test="ourCompany != null and ourCompany !=''">
        and our_company like concat('%',#{ourCompany},'%')
      </if>
      <if test="counterCompany != null and counterCompany !=''">
        and counter_company like concat('%',#{counterCompany},'%')
      </if>
      <if test="departmentIdPath != null and departmentIdPath !='' ">
        and department_id_path like concat('%',#{departmentIdPath},'%')
      </if>
      <if test="financeUserNo != null and financeUserNo !=''">
        and finance_user_no like concat('%',#{financeUserNo},'%')
      </if>
      <if test="lastUpdateNo != null and lastUpdateNo !=''">
        and updator_no = #{lastUpdateNo}
      </if>
      <if test="poStatus != null">
        and po_status = #{poStatus}
      </if>
    </where>
  </select>

</mapper>