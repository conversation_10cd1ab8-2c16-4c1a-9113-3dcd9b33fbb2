<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.BidSupplierInfoMapper" > 

	<sql id="BaseColumn">
          id     id,
          source_id     sourceId,
          supplier_name     supplierName,
          supplier_address     supplierAddress,
          found_time     foundTime,
          registered_capital     registeredCapital,
          staff_size     staffSize,
          contact_name     contactName,
          contact_phone     contactPhone,
          contact_mail     contactMail,
          is_win_bid     isWinBid,
          audit_status  auditStatus,
          bond_audit_status  bondAuditStatus,
          is_valid     isValid,
          create_time     createTime,
          creator_no     creator<PERSON>o,
          creator     creator,
          update_time     updateTime,
          updator_no     updatorNo,
          updator     updator
    </sql>

    <delete id="deleteByIds">
        delete from bid_supplier_info where id in
        <foreach collection="list" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.purchasebid.model.BidSupplierInfo">
	    select 
	    <include refid="BaseColumn"/>
	    from bid_supplier_info
	    where id = #{id}
	</select>

    <select id="selectBySourceId" resultType="com.xhs.oa.purchasebid.model.BidSupplierInfo">
        select <include refid="BaseColumn"/>
        from bid_supplier_info
        where is_valid=1 and source_id=#{sourceId}
    </select>

    <select id="findSupplierAuditStatusBySourceId" resultType="com.xhs.oa.purchasebid.dto.SupplierAuditResultDto">
        select
          id supplierId,
          audit_status auditResult,
          bond_audit_status  bondAuditResult
        from bid_supplier_info
        where is_valid=1 and source_id=#{sourceId}
    </select>

    <select id="findAuditPassSuppliers" resultType="com.xhs.oa.purchasebid.model.BidSupplierInfo">
        select
          id id,
          supplier_name supplierName
        from bid_supplier_info
        where is_valid=1 and source_id=#{sourceId} and (bond_audit_status=#{auditStatus} or (bond_audit_status=#{noNeedauditStatus} and audit_status=#{auditStatus}))
    </select>

    <select id="findWinBidSuppliers" resultType="com.xhs.oa.purchasebid.model.BidSupplierInfo">
        select
          source_id sourceId,
          supplier_name supplierName
        from bid_supplier_info
        where is_valid=1 and is_win_bid=1 and source_id in
        <foreach collection="list" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <select id="findFormNumById" resultType="java.lang.String">
        select
          bps.form_num
        from bid_supplier_info bsi
        join bid_purchase_source bps on bsi.source_id=bps.id
        where bsi.id=#{id}
    </select>

    <select id="selectByPrimaryKeys" resultType="com.xhs.oa.purchasebid.model.BidSupplierInfo">
        select <include refid="BaseColumn"></include>
        from bid_supplier_info
        where id in
        <foreach collection="list" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        order by update_time desc
    </select>

    <select id="queryPageWinBidSupplierInfos" resultType="com.xhs.oa.purchasebid.model.BidSupplierInfo">
        select <include refid="BaseColumn"></include>
        from bid_supplier_info
        where is_valid=1
        order by update_time desc
    </select>

    <select id="querySourceBySupplierName" resultType="com.xhs.oa.purchasebid.dto.SupplierTransferDTO">
        select
            bps.id  sourceId,
            bps.form_num  formNum,
            bps.creator_no  creatorNo,
            bps.creator  creator,
            bps.create_time  createTime,
            bsi.id  supplierId,
            bsi.contact_name  contactName,
            bsi.contact_phone  contactPhone,
            bsi.contact_mail  contactMail
        from bid_supplier_info bsi
        join bid_purchase_source bps on bsi.source_id=bps.id and bps.is_valid=1
        where bsi.is_valid=1 and bsi.supplier_name=#{supplierName}
    </select>


    <insert id="batchInsertSupplierInfo" parameterType="java.util.List" keyProperty="id" useGeneratedKeys="true" >
        insert into bid_supplier_info (
          source_id,
          supplier_name,
          supplier_address,
          found_time,
          registered_capital,
          staff_size,
          contact_name,
          contact_phone,
          contact_mail,
          is_win_bid,
          audit_status,
          bond_audit_status,
          is_valid,
          create_time,
          creator_no,
          creator,
          update_time,
          updator_no,
          updator
      )
    values
    <foreach collection="list" item="item" separator=",">
    (
           #{item.sourceId},
           #{item.supplierName},
           #{item.supplierAddress},
           #{item.foundTime},
           #{item.registeredCapital},
           #{item.staffSize},
           #{item.contactName},
           #{item.contactPhone},
           #{item.contactMail},
           #{item.isWinBid},
           #{item.auditStatus},
           #{item.bondAuditStatus},
           1,
           now(),
           #{item.creatorNo},
           #{item.creator},
           now(),
           #{item.updatorNo},
          #{item.updator}
    )
    </foreach>
    </insert>

    <update id="updateAuditStatus">
        update bid_supplier_info
        set updator_no=#{userId}, updator=#{userName}, audit_status=#{targetStatus}
        where is_valid=1 and id=#{id}
    </update>

    <update id="batchUpdateAuditStatus">
        update bid_supplier_info
        set updator_no=#{userId}, updator=#{userName}, audit_status=#{targetStatus}
        where is_valid=1 and source_id=#{sourceId} and audit_status=#{originStatus}
    </update>

    <update id="updateSuppliers">
        <foreach collection="list" item="item" separator=";">
            update bid_supplier_info
            set
                supplier_name = #{item.supplierName},

                supplier_address = #{item.supplierAddress},

                found_time = #{item.foundTime},

                registered_capital = #{item.registeredCapital},

                staff_size = #{item.staffSize},

                contact_name = #{item.contactName},

                contact_phone = #{item.contactPhone},

                contact_mail = #{item.contactMail},

                is_win_bid = #{item.isWinBid},

                audit_status = #{item.auditStatus},

                bond_audit_status = #{item.bondAuditStatus},

                update_time = now(),

                updator_no = #{item.updatorNo},

                updator = #{item.updator}

            where id = #{item.id}
        </foreach>
    </update>

    <update id="updateWinBidSupplierStatus">
        <foreach collection="list" item="item" separator=";">
            update bid_supplier_info
            set is_win_bid=1
            where id=#{item}
        </foreach>
    </update>

    <update id="updateBondAuditStatus">
        update bid_supplier_info
        set updator_no=#{userId}, updator=#{userName}, bond_audit_status=#{targetStatus}
        where is_valid=1 and id=#{id}
    </update>

</mapper>   
