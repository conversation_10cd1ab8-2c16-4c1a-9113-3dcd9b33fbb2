<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.BidRatingResultMapper" > 

	<sql id="BaseColumn">
          id     id,
          supplier_id     supplierId,
          judge_id     judgeId,
          rating_item_id     ratingItemId,
          score     score,
          old_score     oldScore,
          standard     standard,
          operator_no     operatorNo,
          operator     operator,
          create_time     createTime,
          update_time     updateTime
    </sql>

    <delete id="deleteBySupplierAndJudgeId">
        delete from bid_rating_result
        where supplier_id=#{supplierId} and judge_id=#{judgeId}
    </delete>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.purchasebid.model.BidRatingResult">
	    select 
	    <include refid="BaseColumn"/>
	    from bid_rating_result
	    where id = #{id}
	</select>

    <select id="findRatingResultBySourceId" resultType="com.xhs.oa.purchasebid.dto.RatingResultDto">
        select
          brr.rating_item_id itemId,
          bri.rating_item_type itemType,
          brr.score score,
          brr.old_score  oldScore,
          brr.judge_id judgeId,
          brr.supplier_id supplierId
        from t_supplier_service bsi
        join bid_rating_result brr on brr.supplier_id=bsi.id
        join bid_rating_item bri on brr.rating_item_id=bri.id
        where bri.is_valid=1
        and bri.rating_item_type=#{itemType} and bsi.source_form_num=#{sourceFormNum}
    </select>

    <select id="findSupplierRatingResult" resultType="com.xhs.oa.purchasebid.model.BidRatingResult">
        select <include refid="BaseColumn"/>
        from bid_rating_result
        where judge_id=#{judgeId} and supplier_id=#{supplierId}
    </select>

    <select id="selectByUniqueKey" resultType="com.xhs.oa.purchasebid.model.BidRatingResult">
        select <include refid="BaseColumn"/>
        from bid_rating_result
        where supplier_id=#{supplierId} and judge_id=#{judgeId} and rating_item_id=#{itemId}
    </select>

    <select id="findRatingResultBySourceIdAndJudgeId" resultType="com.xhs.oa.purchasebid.dto.RatingResultDto">
        select
          bri.rating_item_type itemType,
          brr.score score,
          brr.supplier_id supplierId
        from bid_rating_result brr
        join bid_rating_item bri on brr.rating_item_id=bri.id
        where bri.is_valid=1
        and bri.bidding_id=#{biddingId} and brr.judge_id=#{judgeId}
    </select>

    <select id="findSupplierPriceScores" resultType="com.xhs.oa.purchasebid.model.BidRatingResult">
        select
          brr.old_score oldScore,
          brr.supplier_id supplierId
        from bid_rating_result brr
        join bid_rating_item bri on brr.rating_item_id=bri.id
        where bri.is_valid=1
        and bri.bidding_id=#{biddingId} and brr.judge_id=#{judgeId} and bri.rating_item_type=#{itemType}
    </select>


    <insert id="insert" parameterType="com.xhs.oa.purchasebid.model.BidRatingResult">
    insert into bid_rating_result (
          id,
          supplier_id,
          judge_id,
          rating_item_id,
          score,
          old_score,
          standard,
          operator_no,
          operator,
          create_time,
          update_time
      )
    values (
           #{id},
           #{supplierId},
           #{judgeId},
           #{ratingItemId},
           #{score},
           #{oldScore},
           #{standard},
           #{operatorNo},
           #{operator},
           #{createTime},
          #{updateTime}
      )
  </insert>

    <insert id="batchInsert">
    insert into bid_rating_result (
          supplier_id,
          judge_id,
          rating_item_id,
          score,
          standard,
          operator_no,
          operator,
          create_time,
          update_time
      )
    values
    <foreach collection="list" item="item" separator=",">
        (
        #{item.supplierId},
        #{item.judgeId},
        #{item.ratingItemId},
        #{item.score},
        #{item.standard},
        #{item.operatorNo},
        #{item.operator},
        now(),
        now()
        )
    </foreach>
    </insert>


    <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidRatingResult">
    update bid_rating_result
    set 
           id = #{id},

           supplier_id = #{supplierId},

           judge_id = #{judgeId},

           rating_item_id = #{ratingItemId},

           score = #{score},

           old_score = #{oldScore},

           standard = #{standard},

           operator_no = #{operatorNo},

           operator = #{operator},

           create_time = #{createTime},

         update_time = #{updateTime}
    where id = #{id}
  </update>

    <update id="updateByUniqueKey">
        update bid_rating_result
        set score=#{score}, old_score=#{oldScore}, operator_no=#{operatorNo}, operator=#{operator}, update_time=now()
        where supplier_id=#{supplierId} and judge_id=#{judgeId} and rating_item_id=#{ratingItemId}
    </update>

    <update id="updateSupplierIds">
        update bid_rating_result
        set supplier_id=#{targetId}, flag=1
        where supplier_id=#{originId} and flag=0
    </update>

</mapper>   
