<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.BidOrderPaymentMapper" >

    <sql id="BaseColumn">
        id     id,
          form_num     formNum,
          payment_form_type     paymentFormType,
          payment_type     paymentType,
          payment_condition  paymentCondition,
          order_form_num     orderFormNum,
          expected_payment_time     expectedPaymentTime,
          this_payment_amount     thisPaymentAmount,
          payment_status     paymentStatus,
          is_pause     isPause,
          is_repaid    isRepaid,
          creator_no     creator<PERSON>o,
          creator     creator,
          create_time     createTime,
          updator_no     updatorNo,
          updator     updator,
          update_time     updateTime,
          current_step  currentStep,
          currency  currency,
          payment_method paymentMethod,
          payment_stage paymentStage,
          invoice_status invoiceStatus,
          payment_company paymentCompany,
          gathering_company gatheringCompany,
          pay_show_status payShowStatus,
          supplier_no supplierNo
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.purchasebid.model.BidOrderPayment">
	    select 
	    <include refid="BaseColumn"/>
	    from bid_order_payment
	    where id = #{id}
	</select>

    <select id="selectByPaymentFormNum" resultType="com.xhs.oa.purchasebid.model.BidOrderPayment">
        select
        <include refid="BaseColumn"/>
        from bid_order_payment
        where form_num=#{paymentFormNum}
    </select>

    <select id="selectByOrderFormNumAndTypeAndStatus" resultType="com.xhs.oa.purchasebid.model.BidOrderPayment">
        select <include refid="BaseColumn"/>
        from bid_order_payment
        where order_form_num=#{orderFormNum} and payment_type=#{type} and payment_status in
        <foreach collection="status" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <sql id="PageQueryColumn">
          bop.id  paymentId,
          bop.form_num  formNum,
          cf.audit_status  formAuditStatus,
          cf.current_step  currentStep,
          cf.current_audit_user  currentAuditor,
          bop.order_form_num  orderFormNum,
          bop.payment_type  payType,
          bop.payment_status  payStatus,
          bop.is_pause  isPause,
          bop.is_repaid  isRepaid,
          bpo.currency  currency,
          bop.this_payment_amount  formAmount,
          bpo.already_paid  paidAmount,
          bpo.total_origin_amount  totalAmount,
          bop.expected_payment_time  expectedPayTime,
          cf.creator_no  creatorNo,
          cf.be_entrusted_id  beEntrustedId
    </sql>

    <select id="queryPaymentPageDtos" resultType="com.xhs.oa.purchasebid.dto.PurchasePaymentPageDto">
        select
        <include refid="PageQueryColumn"/>
        <include refid="PageQuery"/>

        union

        select
        <include refid="PageQueryColumn"/>
        <include refid="FormShareList"/>

        order by paymentId desc
        limit #{start}, #{pageSize}
    </select>

    <sql id="FormShareList">
        from t_form_share_auth fsa
        join common_form cf on fsa.form_num = cf.form_num and cf.is_valid=1
        join bid_order_payment bop on bop.form_num=fsa.form_num
        left join bid_purchase_order bpo on bop.order_form_num=bpo.form_num and bpo.is_valid=1
        where fsa.is_valid=1
        and ( (fsa.auth_value=#{shareUserId} and fsa.auth_type='personal')
            <if test="shareDeptIds!=null and shareDeptIds.size()>0">
                or (fsa.auth_value in
                <foreach collection="shareDeptIds" item="shareDeptId" separator="," open="(" close=")">
                    #{shareDeptId}
                </foreach>
                and fsa.auth_type='department')
            </if>
            )

            <if test="formNum != null and formNum != '' ">
                and bop.form_num like concat('%',#{formNum},'%')
            </if>

            <if test="formAuditStatus != null and formAuditStatus != '' ">
                and  cf.audit_status = #{formAuditStatus}
            </if>

            <if test="beginAmount != null and endAmount != null">
                <![CDATA[ and bop.this_payment_amount >= #{beginAmount} and bop.this_payment_amount <= #{endAmount} ]]>
            </if>

            <if test="supplierName != null and supplierName != '' ">
                and bpo.supplier_name like concat('%',#{supplierName},'%')
            </if>

            <if test="paymentStatus != null and paymentStatus != '' ">
                and bop.payment_status = #{paymentStatus}
            </if>
    </sql>

    <select id="queryPaymentPageDtoCount" resultType="java.lang.Integer">
        select count(1)
        from (
            select cf.form_num
            <include refid="PageQuery"/>
            union
            select cf.form_num
            <include refid="FormShareList"/>
        ) temp
    </select>

    <sql id="PaymentExportColumn">
        bop.id  paymentId,
        bop.form_num  formNum,
        cf.audit_status  formAuditStatus,
        cf.current_step  currentStep,
        cf.current_audit_user  currentAuditor,
        bop.order_form_num  orderFormNum,
        bop.payment_type  payType,
        bop.payment_status  payStatus,
        bpo.currency  currency,
        bop.this_payment_amount  formAmount,
        bop.is_pause  isPause,
        bop.is_repaid  isRepaid,
        bpo.already_paid  paidAmount,
        bpo.total_origin_amount  totalAmount,
        bop.expected_payment_time  expectedPayTime
    </sql>

    <select id="queryPaymentPageDtosForExport" resultType="com.xhs.oa.purchasebid.dto.PurchasePaymentPageDto">
        select
        <include refid="PaymentExportColumn"/>
        <include refid="PageQuery"/>

        union

        select
        <include refid="PaymentExportColumn"/>
        <include refid="FormShareList"/>

        order by paymentId desc
    </select>

    <select id="findPaymentDetail" resultType="com.xhs.oa.purchasebid.dto.PaymentDetailDto">
        select
          bop.form_num  paymentFormNum,
          bop.payment_type  paymentType,
          bop.this_payment_amount  payAmount,
          bop.payment_status  paymentStatus,
          fpd.payment_no  paymentNo,
          fpd.payment_success_time  paymentTime,
          fpd.gathering_name  gatheringCompany,
          fpd.bank_name  gatheringBank,
          fpd.gathering_account  gatheringAccount,
          fpd.reason  failReason
        from bid_order_payment bop
        left join form_payment_detail fpd on bop.form_num=fpd.form_no
        where bop.order_form_num=#{orderFormNum}
    </select>

    <select id="findAlreadyPaidAmount" resultType="java.math.BigDecimal">
        select sum(this_payment_amount)
        from bid_order_payment
        where payment_status=#{status} and order_form_num=#{orderFormNum}
    </select>

    <select id="findPauseForms" resultType="java.lang.String">
        select
          form_num  formNum
        from bid_order_payment
        where is_pause=1 and form_num in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="querySpecialPaymentFormNums" resultType="java.lang.String">
        select
          bop.form_num
        from bid_order_payment bop
        join common_form cf on bop.form_num=cf.form_num and cf.is_valid=1
        where (cf.creator_no=#{userId} or cf.be_entrusted_id=#{userId}) and cf.audit_status=#{auditStatus} and cf.form_type=#{formType} and bop.form_num like concat('%',#{key},'%')
    </select>

    <select id="batchQueryPayTypeAndOrderNum" resultType="com.xhs.oa.purchasebid.model.BidOrderPayment">
        select
          form_num  formNum,
          payment_type     paymentType,
          order_form_num     orderFormNum,
          payment_status   paymentStatus,
          pay_show_status  payShowStatus
        from bid_order_payment
        where form_num in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryByPayTypeAndOrderNum" resultType="com.xhs.oa.purchasebid.model.BidOrderPayment">
        select <include refid="BaseColumn"/>
        from bid_order_payment
        where order_form_num=#{orderFormNum} and payment_type=#{payType}
        <if test="currentFormNum != null">
            and form_num!=#{currentFormNum}
        </if>
    </select>

    <select id="findUserSpecialPaymentCount" resultType="java.lang.Integer">
        select count(1)
        from bid_order_payment bop
        join common_form cf on bop.form_num=cf.form_num and cf.is_valid=1
        where cf.audit_status=#{status} and bop.creator_no=#{userId} and bop.payment_form_type=#{type}
    </select>

    <select id="queryByPayConditionAndPayType" resultType="com.xhs.oa.purchasebid.dto.PaymentInvoiceNoticeJobDTO">
        select
          bpo.form_num orderFormNum,
          bpo.currency currency,
          bpo.invoice_difference invoiceDiff,
          bop.form_num paymentFormNum,
          fpd.payment_success_time paymentSuccessTime,
          bpo.creator_no creatorNo
        from bid_order_payment bop
        left join form_payment_detail fpd on fpd.form_no=bop.form_num
        join bid_purchase_order bpo on bop.order_form_num=bpo.form_num and bpo.is_valid=1
        where bpo.invoice_difference>#{invoiceDiff} and bop.payment_status=#{paymentStatus}
            and bop.payment_type in
            <foreach collection="paymentTypes" item="type" separator="," open="(" close=")">
                #{type}
            </foreach>
            and bop.payment_condition in
            <foreach collection="paymentConditions" item="condition" separator="," open="(" close=")">
                #{condition}
            </foreach>
        order by bpo.id
        limit #{start}, #{pageSize}
    </select>

    <select id="findPaymentDetailBySpecialFormNum" resultType="com.xhs.oa.purchasebid.dto.PaymentDetailDto">
        select
          bop.form_num  paymentFormNum,
          bop.payment_type  paymentType,
          bop.this_payment_amount  payAmount,
          bop.payment_status  paymentStatus,
          fpd.payment_no  paymentNo,
          fpd.payment_success_time  paymentTime,
          fpd.gathering_name  gatheringCompany,
          fpd.bank_name  gatheringBank,
          fpd.gathering_account  gatheringAccount
        from bid_order_payment bop
        left join form_payment_detail fpd on bop.form_num=fpd.form_no
        where bop.form_num=#{specialFormNum}
    </select>

    <select id="findAlreadyPaidAmountByType" resultType="java.math.BigDecimal">
        select sum(this_payment_amount)
        from bid_order_payment
        where order_form_num=#{orderFormNum}
        and payment_status in
        <foreach collection="statusList" item="status" separator="," open="(" close=")">
            #{status}
        </foreach>
        and payment_type=#{paymentType}
    </select>

    <select id="queryPaymentStatusByProcInstId" resultType="java.lang.String">
        select bop.payment_status paymentStatus
        from bid_order_payment bop
        join common_form cf on bop.form_num=cf.form_num and cf.is_valid=1
        where cf.process_instance_id=#{procInstId}
    </select>

    <select id="selectByOrderFormNumAndStatus" resultType="com.xhs.oa.purchasebid.model.BidOrderPayment">
        select <include refid="BaseColumn"/>
        from bid_order_payment
        where order_form_num=#{orderFormNum} and payment_status in
        <foreach collection="status" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryByFormNum" resultType="com.xhs.oa.purchasebid.model.BidOrderPayment">
        select
        <include refid="BaseColumn"/>
        from bid_order_payment
        where form_num = #{formNum}
    </select>

    <sql id="PageQuery">
        from bid_order_payment bop
        left join bid_purchase_order bpo on bop.order_form_num=bpo.form_num and bpo.is_valid=1
        join common_form cf on bop.form_num=cf.form_num
        where cf.is_valid=1

        <if test="userId != null">
            and (cf.creator_no = #{userId} or cf.be_entrusted_id = #{userId})
        </if>

        <if test="formNum != null and formNum != '' ">
            and bop.form_num like concat('%',#{formNum},'%')
        </if>

        <if test="formAuditStatus != null and formAuditStatus != '' ">
            and  cf.audit_status = #{formAuditStatus}
        </if>

        <if test="beginAmount != null and endAmount != null">
            <![CDATA[ and bop.this_payment_amount >= #{beginAmount} and bop.this_payment_amount <= #{endAmount} ]]>
        </if>

        <if test="supplierName != null and supplierName != '' ">
            and bpo.supplier_name like concat('%',#{supplierName},'%')
        </if>

        <if test="paymentStatus != null and paymentStatus != '' ">
            and bop.payment_status = #{paymentStatus}
        </if>
    </sql>


    <insert id="insert" parameterType="com.xhs.oa.purchasebid.model.BidOrderPayment">
    insert into bid_order_payment (
          form_num,
          payment_form_type,
          payment_type,
          payment_condition,
          order_form_num,
          expected_payment_time,
          this_payment_amount,
          payment_status,
          pay_show_status,
          is_pause,
          is_repaid,
          creator_no,
          creator,
          create_time,
          updator_no,
          updator,
          update_time,
          supplier_no
      )
    values (
           #{formNum},
           #{paymentFormType},
           #{paymentType},
           #{paymentCondition},
           #{orderFormNum},
           #{expectedPaymentTime},
           #{thisPaymentAmount},
           #{paymentStatus},
           #{payShowStatus},
           0,
           0,
           #{creatorNo},
           #{creator},
           now(),
           #{updatorNo},
           #{updator},
           now(),
           #{supplierNo}
      )
  </insert>

    <insert id="insertSelective" parameterType="com.xhs.oa.purchasebid.model.BidOrderPayment">
        insert into bid_order_payment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="formNum != null">
                form_num,
            </if>
            <if test="paymentFormType != null">
                payment_form_type,
            </if>
            <if test="paymentType != null">
                payment_type,
            </if>
            <if test="paymentCondition != null">
                payment_condition,
            </if>
            <if test="orderFormNum != null">
                order_form_num,
            </if>
            <if test="expectedPaymentTime != null">
                expected_payment_time,
            </if>
            <if test="thisPaymentAmount != null">
                this_payment_amount,
            </if>
            <if test="paymentStatus != null">
                payment_status,
            </if>
            <if test="isPause != null">
                is_pause,
            </if>
            <if test="isRepaid != null">
                is_repaid,
            </if>
            <if test="creatorNo != null">
                creator_no,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updatorNo != null">
                updator_no,
            </if>
            <if test="updator != null">
                updator,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="currentStep != null">
                current_step,
            </if>
            <if test="currency != null">
                currency,
            </if>
            <if test="paymentMethod != null">
                payment_method,
            </if>
            <if test="paymentStage != null">
                payment_stage,
            </if>
            <if test="invoiceStatus != null">
                invoice_status,
            </if>
            <if test="paymentCompany != null">
                payment_company,
            </if>
            <if test="gatheringCompany != null">
                gathering_company,
            </if>
            <if test="payShowStatus != null">
                pay_show_status,
            </if>
            <if test="supplierNo != null">
                supplier_no,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="formNum != null">
                #{formNum,jdbcType=VARCHAR},
            </if>
            <if test="paymentFormType != null">
                #{paymentFormType,jdbcType=VARCHAR},
            </if>
            <if test="paymentType != null">
                #{paymentType,jdbcType=VARCHAR},
            </if>
            <if test="paymentCondition != null">
                #{paymentCondition,jdbcType=VARCHAR},
            </if>
            <if test="orderFormNum != null">
                #{orderFormNum,jdbcType=VARCHAR},
            </if>
            <if test="expectedPaymentTime != null">
                #{expectedPaymentTime,jdbcType=TIMESTAMP},
            </if>
            <if test="thisPaymentAmount != null">
                #{thisPaymentAmount,jdbcType=DECIMAL},
            </if>
            <if test="paymentStatus != null">
                #{paymentStatus,jdbcType=VARCHAR},
            </if>
            <if test="isPause != null">
                #{isPause,jdbcType=TINYINT},
            </if>
            <if test="isRepaid != null">
                #{isRepaid,jdbcType=TINYINT},
            </if>
            <if test="creatorNo != null">
                #{creatorNo,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatorNo != null">
                #{updatorNo,jdbcType=VARCHAR},
            </if>
            <if test="updator != null">
                #{updator,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="currentStep != null">
                #{currentStep,jdbcType=VARCHAR},
            </if>
            <if test="currency != null">
                #{currency,jdbcType=VARCHAR},
            </if>
            <if test="paymentMethod != null">
                #{paymentMethod,jdbcType=VARCHAR},
            </if>
            <if test="paymentStage != null">
                #{paymentStage,jdbcType=VARCHAR},
            </if>
            <if test="invoiceStatus != null">
                #{invoiceStatus,jdbcType=VARCHAR},
            </if>
            <if test="paymentCompany != null">
                #{paymentCompany,jdbcType=VARCHAR},
            </if>
            <if test="gatheringCompany != null">
                #{gatheringCompany,jdbcType=VARCHAR},
            </if>
            <if test="payShowStatus != null">
                #{payShowStatus,jdbcType=INTEGER},
            </if>
            <if test="supplierNo != null">
                #{supplierNo,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>


    <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidOrderPayment">
        update bid_order_payment
        set
        <if test="formNum != null and formNum != ''">
            form_num              = #{formNum},
        </if>
        <if test="paymentFormType != null and paymentFormType != ''">
            payment_form_type     = #{paymentFormType},
        </if>
        <if test="paymentType != null and paymentType != ''">
            payment_type          = #{paymentType},
        </if>
        <if test="paymentCondition != null and paymentCondition != ''">
            payment_condition     = #{paymentCondition},
        </if>
        <if test="orderFormNum != null and orderFormNum != ''">
            order_form_num        = #{orderFormNum},
        </if>
        <if test="expectedPaymentTime != null ">
            expected_payment_time = #{expectedPaymentTime},
        </if>
        <if test="thisPaymentAmount != null">
            this_payment_amount   = #{thisPaymentAmount},
        </if>
        <if test="paymentStatus != null and paymentStatus != ''">
            payment_status        = #{paymentStatus},
        </if>
        <if test="isPause != null">
            is_pause              = #{isPause},
        </if>
        <if test="isRepaid != null and isRepaid != ''">
            is_repaid             = #{isRepaid},
        </if>
        <if test="creatorNo != null and creatorNo != ''">
            creator_no            = #{creatorNo},
        </if>
        <if test="creator != null and creator != ''">
            creator               = #{creator},
        </if>
        <if test="createTime != null">
            create_time           = #{createTime},
        </if>
        <if test="updatorNo != null and updatorNo != ''">
            updator_no            = #{updatorNo},
        </if>
        <if test="updator != null and updator != ''">
            updator               = #{updator},
        </if>
        <if test="currentStep != null and currentStep != ''">
            current_step          = #{currentStep},
        </if>
        <if test="currency != null and currency != ''">
            currency              = #{currency},
        </if>
        <if test="paymentMethod != null and paymentMethod != ''">
            payment_method        = #{paymentMethod},
        </if>
        <if test="paymentStage != null and paymentStage != ''">
            payment_stage         = #{paymentStage},
        </if>
        <if test="invoiceStatus != null and invoiceStatus != ''">
            invoice_status        = #{invoiceStatus},
        </if>
        <if test="paymentCompany != null and paymentCompany != ''">
            payment_company       = #{paymentCompany},
        </if>
        <if test="gatheringCompany != null and gatheringCompany != ''">
            gathering_company     = #{gatheringCompany},
        </if>
        <if test="payShowStatus != null">
            pay_show_status = #{payShowStatus},
        </if>
        <if test="supplierNo != null and supplierNo != ''">
           supplier_no = #{supplierNo },
        </if>
        update_time = now()
        where id = #{id}
    </update>

    <update id="updatePauseByFormNum">
        update bid_order_payment
        set is_pause=#{targetPause}, updator_no=#{userId}, updator=#{userName}, update_time=now()
        where form_num=#{formNum} and is_pause=#{originPause}
    </update>

    <update id="updateExpectPayTimeAndStatus">
        update bid_order_payment
        set expected_payment_time=#{payTime}, payment_status=#{paymentStatus}, updator_no=#{userId}, updator=#{userName}, update_time=now()
        where form_num=#{formNum}
    </update>

    <update id="updatePaymentStatus">
        update bid_order_payment
        set payment_status=#{target}, updator_no=#{userId}, updator=#{userName}, update_time=now()
        where form_num=#{formNum} and is_pause=0 and payment_status=#{origin}
    </update>

    <update id="updatePaymentStatusWithoutCheck">
        update bid_order_payment
        set payment_status=#{target}, updator_no=#{userId}, updator=#{userName}, update_time=now()
        where form_num=#{formNum} and is_pause=0
    </update>

    <update id="updateIsRepaid">
        update bid_order_payment
        set is_repaid=1, updator_no=#{userId}, updator=#{userName}, update_time=now()
        where form_num=#{paymentFormNum} and is_repaid=0
    </update>

    <update id="updatePaymentStatusWithTime">
        update bid_order_payment
        set payment_status=#{target}, updator_no=#{userId}, updator=#{userName}, update_time=now()
        where payment_status=#{origin} and  <![CDATA[ expected_payment_time<=#{time} ]]>
    </update>

    <update id="batchUpdatePaymentStatus">
        update bid_order_payment
        set payment_status=#{targetStatus}, updator_no=#{userId}, updator=#{userName}, update_time=now()
        where form_num in
        <foreach collection="formNums" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <update id="batchUpdatePaymentStatusWithCheck">
        update bid_order_payment
        set payment_status=#{targetStatus}, updator_no=#{userId}, updator=#{userName}, update_time=now()
        where payment_status in
        <foreach collection="originStatusList" item="originStatus" separator="," open="(" close=")">
            #{originStatus}
        </foreach>
         and form_num in
        <foreach collection="formNums" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateCreator">
        update bid_order_payment
        set creator=#{targetUserName}, creator_no=#{targetUserId}
        where form_num=#{formNum}
    </update>
    <select id="queryByOrderFormNum" resultType="com.xhs.oa.purchasebid.model.BidOrderPayment">
        select
        bop.id     id,
        bop.form_num     formNum,
        bop.payment_form_type     paymentFormType,
        bop.payment_type     paymentType,
        bop.payment_condition  paymentCondition,
        bop.order_form_num     orderFormNum,
        bop.expected_payment_time     expectedPaymentTime,
        bop.this_payment_amount     thisPaymentAmount,
        bop.payment_status     paymentStatus,
        bop.is_pause     isPause,
        bop.is_repaid    isRepaid,
        bop.creator_no     creatorNo,
        bop.creator     creator,
        bop.create_time     createTime,
        bop.updator_no     updatorNo,
        bop.updator     updator,
        bop.update_time     updateTime,
        bop.current_step  currentStep,
        bop.currency  currency,
        bop.payment_method paymentMethod,
        bop.payment_stage paymentStage,
        bop.invoice_status invoiceStatus,
        bop.payment_company paymentCompany,
        bop.gathering_company gatheringCompany
        from bid_order_payment bop
        join common_form cf on bop.form_num=cf.form_num
        where cf.is_valid=1
        and bop.order_form_num  = #{orderFormNum}
        and cf.audit_status in(1,2)
        <if test="paymentType!=null">
            and bop.payment_type=#{paymentType}
        </if>
        <if test="paymentStage!=null">
            and bop.payment_stage = #{paymentStage}
        </if>
    </select>

    <update id="updateExpectedTime" parameterType="com.xhs.oa.purchasebid.model.BidOrderPayment">
        update bid_order_payment
        set expected_payment_time=#{expectedPaymentTime},
            updator_no=#{updatorNo},
            updator= #{updator},
            update_time=now()
       where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="batchUpdateShowPayStatus">
        UPDATE bid_order_payment
        SET
        <if test="currentStepKey != null and currentStepKey != ''">
            current_step = #{currentStepKey},
        </if>
            pay_show_status = #{payShowStatus}
        WHERE form_num IN
        <foreach collection="formNums" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="queryPaymentInfoByOrderNo"  resultType="java.lang.String">
        select bop.form_num formNum from bid_order_payment bop where bop.order_form_num  = #{orderFormNum}
    </select>
    
    <select id="queryFkInfoByFormNums" resultType="com.xhs.oa.purchasebid.model.BidOrderPayment">
        select <include refid="BaseColumn"></include>
        from bid_order_payment
        where form_num in
        <foreach collection="formNums" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>   
