<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.BidBatchVoucherMapper" > 

	<sql id="BaseColumn">
          id     id,
          batch_voucher_num     batchVoucherNum,
          supplier_name     supplierName,
          voucher_type  voucherType,
          total_amount     totalAmount,
          receive_time  receiveTime,
          audit_status     auditStatus,
          is_valid     isValid,
          creator_no     creator<PERSON><PERSON>,
          creator     creator,
          create_time     createTime,
          update_time     updateTime
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.purchasebid.model.BidBatchVoucher">
	    select 
	    <include refid="BaseColumn"/>
	    from bid_batch_voucher
	    where id = #{id}
	</select>

    <select id="selectByBatchNum" resultType="com.xhs.oa.purchasebid.model.BidBatchVoucher">
        select <include refid="BaseColumn"/>
        from bid_batch_voucher
        where batch_voucher_num=#{batchNum}
    </select>

    <sql id="PageQueryColumn">
          bbv.id  id,
          bbv.batch_voucher_num  batchVoucherNum,
          bbv.supplier_name  supplierName,
          bbv.total_amount  totalAmount,
          bbv.receive_time  receiveTime,
          bbv.create_time  createTime,
          bbv.audit_status  auditStatus,
          bvpr.order_form_num poFormNum
    </sql>

    <select id="queryVoucherAuditPage" resultType="com.xhs.oa.purchasebid.dto.VoucherPageDto">
        select
        <include refid="PageQueryColumn"/>
        <include refid="PageQuery"/>
        order by bbv.id desc
        limit #{start}, #{pageSize}
    </select>

    <select id="queryVoucherAuditCount" resultType="java.lang.Integer">
        select count(1)
        <include refid="PageQuery"/>
    </select>

    <select id="queryVoucherAuditPageForExport" resultType="com.xhs.oa.purchasebid.dto.VoucherPageDto">
        select
        <include refid="PageQueryColumn"/>
        <include refid="PageQuery"/>
        order by bbv.id desc
    </select>

    <select id="findLatestReceiveTime" resultType="java.util.Date">
        select max(bbv.receive_time)
        from bid_batch_voucher bbv
        join bid_voucher_po_relation bvpr on bvpr.batch_voucher_id=bbv.id
        where bbv.is_valid=1 and bbv.audit_status=#{auditStatus} and bvpr.order_form_num=#{orderFormNum}
    </select>

    <select id="findInvoiceDetailVoByOrderFormNum" resultType="com.xhs.oa.purchasebid.vo.InvoiceDetailVo">
        select
          bbv.batch_voucher_num  batchId,
          bbv.voucher_type  voucherType,
          bv.id  invoiceId,
          bv.invoice_num  invoiceNum,
          bv.amount  amount,
          bbv.audit_status  auditStatus,
          bv.audit_status  invoiceAuditStatus,
          bbv.create_time  receiveTime,
          bv.creator_no  creatorNo
        from bid_batch_voucher bbv
        join bid_voucher bv on bv.batch_voucher_id=bbv.id
        join bid_voucher_po_relation bvpr on bvpr.batch_voucher_id=bbv.id
        where bbv.is_valid=1 and bvpr.order_form_num=#{orderFormNum}
    </select>

    <resultMap id="invoiceOrderMap" type="com.xhs.oa.purchasebid.dto.InvoiceOrderFormDTO">
        <id column="id" property="invoiceId"/>
        <result column="amount" property="invoiceAmount"/>
        <collection property="orderFormNums" ofType="java.lang.String" javaType="list">
            <result column="orderFormNum"/>
        </collection>
    </resultMap>

    <select id="queryInvoiceOrderRelations" resultMap="invoiceOrderMap">
        select
            bv.id  id,
            bv.amount  amount,
            bvpr.order_form_num  orderFormNum
        from bid_voucher bv
        join bid_batch_voucher bbv on bv.batch_voucher_id=bbv.id
        join bid_voucher_po_relation bvpr on bvpr.batch_voucher_id=bbv.id
        where bv.id in
        <foreach collection="list" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <select id="queryByPaymentFormNum" resultType="com.xhs.oa.purchasebid.model.BidBatchVoucher">
        select
            bbv.id     id,
            bbv.batch_voucher_num     batchVoucherNum,
            bbv.supplier_name     supplierName,
            bbv.pay_company    payCompany,
            bbv.voucher_type  voucherType,
            bbv.total_amount     totalAmount,
            bbv.receive_time  receiveTime,
            bbv.audit_status     auditStatus,
            bbv.is_valid     isValid,
            bbv.creator_no     creatorNo,
            bbv.creator     creator,
            bbv.create_time     createTime,
            bbv.update_time     updateTime
        from bid_batch_voucher bbv
                 join bid_voucher_po_relation bvpr on bvpr.batch_voucher_id = bbv.id and bbv.is_valid = 1
        where bvpr.payment_form_num = #{paymentFormNum}
    </select>

    <sql id="PageQuery">
        from bid_batch_voucher bbv
        join bid_voucher_po_relation bvpr on bvpr.batch_voucher_id=bbv.id
        where bbv.is_valid=1
        <if test="userId != null">
            and bbv.creator_no = #{userId}
        </if>

        <if test="auditStatus != null and auditStatus != ''">
            and bbv.audit_status = #{auditStatus}
        </if>

        <if test="formNum!=null and formNum!=''">
            and bvpr.order_form_num like concat('%',#{formNum},'%')
        </if>

        <if test="supplierName != null and supplierName != '' ">
            and bbv.supplier_name like concat('%',#{supplierName},'%')
        </if>

        <if test="beginAmount != null and endAmount != null">
            <![CDATA[ and bbv.total_amount >= #{beginAmount} and bbv.total_amount <= #{endAmount} ]]>
        </if>
    </sql>

    <insert id="insert" parameterType="com.xhs.oa.purchasebid.model.BidBatchVoucher" keyProperty="id" useGeneratedKeys="true">
        insert into bid_batch_voucher (
            batch_voucher_num,
            supplier_name,
            pay_company,
            voucher_type,
            total_amount,
            receive_time,
            audit_status,
            is_valid,
            creator_no,
            creator,
            create_time,
            update_time
        )
        values (
                   #{batchVoucherNum},
                   #{supplierName},
                   #{payCompany},
                   #{voucherType},
                   #{totalAmount},
                   #{receiveTime},
                   #{auditStatus},
                   1,
                   #{creatorNo},
                   #{creator},
                   now(),
                   now()
               )
  </insert>


    <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidBatchVoucher">
        update bid_batch_voucher
        set id=#{id},
        <if test="supplierName != null and supplierName != ''">
            supplier_name = #{supplierName},
        </if>
        <if test="payCompany != null and payCompany != ''">
            pay_company = #{payCompany},
        </if>
        <if test="totalAmount != null">
            total_amount = #{totalAmount},
        </if>
        <if test="voucherType != null and voucherType != ''">
            voucher_type = #{voucherType},
        </if>
        <if test="auditStatus != null and auditStatus != ''">
            audit_status = #{auditStatus},
        </if>
        <if test="receiveTime != null">
            receive_time = #{receiveTime},
        </if>
        <if test="isValid != null">
            is_valid = #{isValid},
        </if>
        update_time = now()
        where id = #{id}
    </update>

    <update id="updateAuditStatusById">
        update bid_batch_voucher
        set audit_status=#{status}, update_time=now()
        where id=#{id} and audit_status=#{originStatus}
    </update>

    <update id="invalidBatchVoucher">
        update bid_batch_voucher
        set is_valid=0, update_time=now()
        where is_valid=1 and batch_voucher_num in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <update id="invalidBatchVoucherIds">
        update bid_batch_voucher
        set is_valid=0, update_time=now()
        where is_valid=1 and id in
        <foreach collection="batchIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateWithoutCheckAuditStatusById">
        update bid_batch_voucher
        set audit_status=#{status}
        where id=#{id}
    </update>

</mapper>   
