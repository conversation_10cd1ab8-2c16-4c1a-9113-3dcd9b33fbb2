<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.BidPurchaseSourceMapper" > 

	<sql id="BaseColumn">
          id     id,
          form_num     formNum,
          submit_type  submitType,
          purchase_amount     purchaseAmount,
          is_change_source_type     isChangeSourceType,
          current_source_type     currentSourceType,
          expected_supplier_count     expectedSupplierCount,
          win_bid_reason  winBidReason,
          is_suspend  isSuspend,
          is_valid  isValid,
          create_time     createTime,
          creator_no     creatorNo,
          creator     creator,
          update_time     updateTime,
          updator_no     updatorNo,
          updator     updator
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.purchasebid.model.BidPurchaseSource">
	    select 
	    <include refid="BaseColumn"/>
	    from bid_purchase_source
	    where id = #{id}
	</select>

    <select id="selectByFormNum" resultType="com.xhs.oa.purchasebid.model.BidPurchaseSource">
        select <include refid="BaseColumn"/>
        from bid_purchase_source
        where form_num=#{formNum}
    </select>


    <insert id="insert" parameterType="com.xhs.oa.purchasebid.model.BidPurchaseSource" keyProperty="id" useGeneratedKeys="true">
    insert into bid_purchase_source (
          form_num,
          submit_type,
          purchase_amount,
          is_change_source_type,
          current_source_type,
          expected_supplier_count,
          win_bid_reason,
          is_suspend,
          is_valid,
          create_time,
          creator_no,
          creator,
          update_time,
          updator_no,
          updator
      )
    values (
           #{formNum},
           #{submitType},
           #{purchaseAmount},
           #{isChangeSourceType},
           #{currentSourceType},
           #{expectedSupplierCount},
           #{winBidReason},
           0,
           1,
           now(),
           #{creatorNo},
           #{creator},
           now(),
           #{updatorNo},
          #{updator}
      )
  </insert>

  
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidPurchaseSource">
    update bid_purchase_source
    set 
           form_num = #{formNum},

           submit_type = #{submitType},

           purchase_amount = #{purchaseAmount},

           is_change_source_type = #{isChangeSourceType},

           current_source_type = #{currentSourceType},

           expected_supplier_count = #{expectedSupplierCount},

           win_bid_reason = #{winBidReason},

           update_time = now(),

           updator_no = #{updatorNo},

         updator = #{updator}
    where id = #{id}
  </update>

    <update id="invalidateSource">
        update bid_purchase_source
        set is_valid=0, updator=#{userName}, updator_no=#{userId}, update_time=now()
        where is_valid=1 and form_num=#{formNum}
    </update>

    <update id="unsuspendSourceProcess">
        update bid_purchase_source
        set is_suspend=0, updator=#{userName}, updator_no=#{userId}, update_time=now()
        where is_valid=1 and is_suspend=1 and id=#{id}
    </update>

    <update id="suspendSourceProcess">
        update bid_purchase_source
        set is_suspend=1, updator=#{userName}, updator_no=#{userId}, update_time=now()
        where is_valid=1 and is_suspend=0 and id=#{id}
    </update>

    <update id="updateExpectedSupplierCountAndReason">
        update bid_purchase_source
        set expected_supplier_count = #{count}, win_bid_reason=#{reason}, updator_no = #{userId}, updator = #{userName}
        where id = #{id}
    </update>

    <update id="updateCurrentBidMode">
        update bid_purchase_source
        set current_source_type=#{bidMode}, updator=#{userName}, updator_no=#{userId}, update_time=now()
        where is_valid=1 and id=#{id}
    </update>

    <update id="updateCreator">
        update bid_purchase_source
        set creator=#{targetUserName}, creator_no=#{targetUserId}
        where form_num=#{formNum}
    </update>


    <select id="queryPurchaseSourceList" parameterType="com.xhs.oa.purchasebid.param.PurchaseSourceParam" resultType="com.xhs.oa.purchasebid.dto.PurchaseResourceDto">
        <include refid="queryPurchaseSourceBaseColumn"/>
        <include refid="queryPurchaseSourceWhere"/>
        order by bs.id desc
        limit #{start},#{pageSize}
    </select>

    <sql id="queryPurchaseSourceBaseColumn">
         select
        cf.create_time     createTime,
        cf.audit_status   formAuditStatus,
        cf.current_step   currentStep,
        cf.current_audit_user currentAuditMan,
        bs.form_num formNum,
        bs.is_suspend  isSuspend,
        bs.purchase_amount purchaseAmount,
        bs.current_source_type sourceType

    </sql>

    <sql id="queryPurchaseSourceWhere">
        from bid_purchase_source bs join
        common_form cf on bs.form_num = cf.form_num  and cf.is_valid = 1
        where bs.is_valid =1
        <if test="userId!=null and userId!=''">
            and (cf.creator_no = #{userId} or cf.be_entrusted_id = #{userId})
        </if>
        <if test="formNum != null and formNum != '' " >
            and bs.form_num like concat('%',#{formNum},'%')
        </if>
        <if test="formAuditStatus != null and formAuditStatus != '' ">
            and  cf.audit_status = #{formAuditStatus}
        </if>
        <if test="beginAmount != null and endAmount != null">
            <![CDATA[ and bs.purchase_amount >= #{beginAmount} and bs.purchase_amount <= #{endAmount} ]]>
        </if>
        <if test="sourceMethod != null and sourceMethod != '' ">
            and bs.current_source_type = #{sourceMethod}
        </if>
    </sql>

    <select id="queryPurchaseSourceListTotalNum" parameterType="com.xhs.oa.purchasebid.param.PurchaseSourceParam" resultType="int">
        select count(1)
        <include refid="queryPurchaseSourceWhere"/>
    </select>

    <select id="queryPurchaseSourceListForExport" parameterType="com.xhs.oa.purchasebid.param.PurchaseSourceParam" resultType="com.xhs.oa.purchasebid.dto.PurchaseResourceDto">
        <include refid="queryPurchaseSourceBaseColumn"/>
        <include refid="queryPurchaseSourceWhere"/>
        order by bs.id desc
    </select>

    <select id="querySourceFormDto" resultType="com.xhs.oa.purchasebid.dto.SourceFormDto">
        select
          bps.id sourceId,
          bps.form_num formNum,
          cf.audit_status auditStatus
        from bid_purchase_source bps
        join common_form cf on bps.form_num=cf.form_num
        where cf.is_valid=1 and bps.is_valid=1 and bps.is_suspend=0
        and bps.form_num in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    
    <resultMap id="sourceRequestDTO" type="com.xhs.oa.purchasebid.dto.SourceRequestDTO">
        <id column="sourceFormNum" property="sourceFormNum"/>
        <result column="sourceType" property="sourceType"/>
        <result column="sourceAmount" property="amount"/>
        <collection property="purchaseRequests" ofType="com.xhs.oa.purchasebid.model.BidPurchaseRequest">
            <id column="requestFormNum" property="formNum"/>
            <result column="projectName" property="puchaseProjectName"/>
        </collection>
    </resultMap>

    <select id="querySourceRequestInfo" resultMap="sourceRequestDTO">
        select
            bps.form_num  sourceFormNum,
            bps.purchase_amount  sourceAmount,
            bps.current_source_type  sourceType,
            bpr.form_num  requestFormNum,
            bpr.puchase_project_name  projectName
        from bid_request_source_relation brsr
        join bid_purchase_source bps on bps.form_num=brsr.source_form_num and bps.is_valid=1
        join bid_purchase_request bpr on bpr.form_num=brsr.request_form_num and bpr.is_valid=1
        where bps.form_num in
        <foreach collection="list" item="sourceFormNum" separator="," open="(" close=")">
            #{sourceFormNum}
        </foreach>
    </select>

</mapper>   
