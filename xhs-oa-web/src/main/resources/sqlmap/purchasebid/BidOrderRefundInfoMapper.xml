<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.BidOrderRefundInfoMapper" > 

	<sql id="BaseColumn">
          id     id,
          take_delivery_id     takeDeliveryId,
          refund_amount     refundAmount,
          refund_time     refundTime,
          supplier_account_name     supplierAccountName,
          supplier_bank_account     supplierBankAccount,
          red_account_name     redAccountName,
          red_bank_account     redBankAccount,
          creator_no     creator<PERSON><PERSON>,
          creator     creator,
          create_time     createTime,
          update_time     updateTime
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.purchasebid.model.BidOrderRefundInfo">
	    select 
	    <include refid="BaseColumn"/>
	    from bid_order_refund_info
	    where id = #{id}
	</select>

    <select id="selectByDeliveryId" resultType="com.xhs.oa.purchasebid.model.BidOrderRefundInfo">
        select
        <include refid="BaseColumn"/>
        from bid_order_refund_info
        where take_delivery_id = #{refId}
    </select>


    <insert id="insert" parameterType="com.xhs.oa.purchasebid.model.BidOrderRefundInfo">
    insert into bid_order_refund_info (
          take_delivery_id,
          refund_amount,
          refund_time,
          supplier_account_name,
          supplier_bank_account,
          red_account_name,
          red_bank_account,
          creator_no,
          creator,
          create_time,
          update_time
      )
    values (
           #{takeDeliveryId},
           #{refundAmount},
           #{refundTime},
           #{supplierAccountName},
           #{supplierBankAccount},
           #{redAccountName},
           #{redBankAccount},
           #{creatorNo},
           #{creator},
           now(),
           now()
      )
  </insert>

  
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidOrderRefundInfo">
    update bid_order_refund_info
    set 
           id = #{id},

           take_delivery_id = #{takeDeliveryId},

           refund_amount = #{refundAmount},

           refund_time = #{refundTime},

           supplier_account_name = #{supplierAccountName},

           supplier_bank_account = #{supplierBankAccount},

           red_account_name = #{redAccountName},

           red_bank_account = #{redBankAccount},

           creator_no = #{creatorNo},

           creator = #{creator},

           create_time = #{createTime},

         update_time = #{updateTime}
    where id = #{id}
  </update>

</mapper>   
