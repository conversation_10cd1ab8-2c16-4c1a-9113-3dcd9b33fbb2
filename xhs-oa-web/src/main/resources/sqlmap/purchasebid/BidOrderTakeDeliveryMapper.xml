<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.BidOrderTakeDeliveryMapper" > 

	<sql id="BaseColumn">
          id     id,
          purchase_order_id     purchaseOrderId,
          receiving_situation     receivingSituation,
          receiving_comment     receivingComment,
          to_pay_amount  toPayAmount,
          is_refund     isRefund,
          creator_no     creator<PERSON><PERSON>,
          creator     creator,
          create_time     createTime,
          update_time     updateTime
    </sql>

    <delete id="deleteByOrderId">
        delete from bid_order_take_delivery where purchase_order_id=#{orderId}
    </delete>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.purchasebid.model.BidOrderTakeDelivery">
	    select 
	    <include refid="BaseColumn"/>
	    from bid_order_take_delivery
	    where id = #{id}
	</select>

    <select id="selectByOrderId" resultType="com.xhs.oa.purchasebid.model.BidOrderTakeDelivery">
        select <include refid="BaseColumn"/>
        from bid_order_take_delivery
        where purchase_order_id=#{orderId}
    </select>

    <select id="findReceiveSituationByOrderId" resultType="java.lang.String">
        select receiving_situation
        from bid_order_take_delivery
        where purchase_order_id=#{orderId}
    </select>


    <insert id="insert" parameterType="com.xhs.oa.purchasebid.model.BidOrderTakeDelivery" useGeneratedKeys="true" keyProperty="id">
    insert into bid_order_take_delivery (
          purchase_order_id,
          receiving_situation,
          receiving_comment,
          to_pay_amount,
          is_refund,
          creator_no,
          creator,
          create_time,
          update_time
      )
    values (
           #{purchaseOrderId},
           #{receivingSituation},
           #{receivingComment},
           #{toPayAmount},
           #{isRefund},
           #{creatorNo},
           #{creator},
           now(),
           now()
      )
  </insert>

  
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidOrderTakeDelivery">
    update bid_order_take_delivery
    set 
           id = #{id},

           purchase_order_id = #{purchaseOrderId},

           receiving_situation = #{receivingSituation},

           receiving_comment = #{receivingComment},

           is_refund = #{isRefund},

           creator_no = #{creatorNo},

           creator = #{creator},

           create_time = #{createTime},

         update_time = #{updateTime}
    where id = #{id}
  </update>

</mapper>   
