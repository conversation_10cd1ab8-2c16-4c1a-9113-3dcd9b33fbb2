<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.BidPurchaseRequestMapper" > 

	<sql id="BaseColumn">
          id     id,
          form_num     formNum,
          puchase_project_name     puchaseProjectName,
          puchase_item_name     puchaseItemName,
          expected_start_time     expectedStartTime,
          expected_purchase_amount     expectedPurchaseAmount,
          confirm_purchase_amount     confirmPurchaseAmount,
          puchase_purpose     puchasePurpose,
          source_times  sourceTimes,
          remain_amount  remainAmount,
          remain_amount_po  remainAmountPo,
          is_valid  isValid,
          create_time     createTime,
          creator_no     creatorNo,
          creator     creator,
          update_time     updateTime,
          updator_no     updatorNo,
          updator     updator
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.purchasebid.model.BidPurchaseRequest">
	    select 
	    <include refid="BaseColumn"/>
	    from bid_purchase_request
	    where id = #{id}
	</select>


  <insert id="insert" parameterType="com.xhs.oa.purchasebid.model.BidPurchaseRequest">
    insert into bid_purchase_request (
          form_num,
          puchase_project_name,
          puchase_item_name,
          expected_start_time,
          expected_purchase_amount,
          confirm_purchase_amount,
          puchase_purpose,
          source_times,
          remain_amount,
          remain_amount_po,
          is_valid,
          create_time,
          creator_no,
          creator,
          update_time,
          updator_no,
          updator
      )
    values (
           #{formNum},
           #{puchaseProjectName},
           #{puchaseItemName},
           #{expectedStartTime},
           #{expectedPurchaseAmount},
           #{confirmPurchaseAmount},
           #{puchasePurpose},
           0,
           #{remainAmount},
           #{remainAmountPo},
           1,
           now(),
           #{creatorNo},
           #{creator},
           now(),
           #{updatorNo},
          #{updator}
      )
  </insert>

  
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidPurchaseRequest">
    update bid_purchase_request
    set 
           id = #{id},

           form_num = #{formNum},

           puchase_project_name = #{puchaseProjectName},

           puchase_item_name = #{puchaseItemName},

           expected_start_time = #{expectedStartTime},

           expected_purchase_amount = #{expectedPurchaseAmount},

           confirm_purchase_amount = #{confirmPurchaseAmount},

           puchase_purpose = #{puchasePurpose},

           create_time = #{createTime},

           creator_no = #{creatorNo},

           creator = #{creator},

           update_time = #{updateTime},

           updator_no = #{updatorNo},

         updator = #{updator}
    where id = #{id}
  </update>


    <update id="updatePurchaseInfoByFormNo" parameterType="com.xhs.oa.purchasebid.model.BidPurchaseRequest">
        update bid_purchase_request
        set
           puchase_project_name = #{puchaseProjectName},

           puchase_item_name = #{puchaseItemName},

           expected_start_time = #{expectedStartTime},

           expected_purchase_amount = #{expectedPurchaseAmount},

           confirm_purchase_amount = #{confirmPurchaseAmount},

           puchase_purpose = #{puchasePurpose},

           remain_amount = #{remainAmount},

           remain_amount_po = #{remainAmountPo},

           update_time = now(),

           updator_no = #{updatorNo},

           updator = #{updator}
        where form_num = #{formNum}
    </update>


    <update id="updatePurchaseConfirmAndRemainAmount" >
        update bid_purchase_request
        set confirm_purchase_amount = #{confirmPurchaseAmount}, remain_amount=#{remainAmount}, remain_amount_po=#{remainAmountPo},
        update_time = now(),
        updator_no = #{updatorNo},
        updator = #{updator}
        where form_num = #{formNum}
    </update>

    <update id="batchIncreaseSourceTimes">
        update bid_purchase_request
        set source_times=(source_times+1),
        update_time = now(),
        updator_no = #{updatorNo},
        updator = #{updator}
        where form_num in
        <foreach collection="formNums" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <update id="batchDecreaseSourceTimes">
        update bid_purchase_request
        set source_times=(source_times-1),
        update_time = now(),
        updator_no = #{updatorNo},
        updator = #{updator}
        where source_times > 0 and form_num in
        <foreach collection="formNums" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <update id="decreaseContractRemainAmount">
        <foreach collection="list" item="item" separator=";">
            update bid_purchase_request
            set remain_amount=#{item.shareAmount},
            update_time = now(),
            updator_no = #{userId},
            updator = #{userName}
            where form_num=#{item.formNum} and remain_amount=#{item.checkAmount} and #{item.shareAmount}>=0
        </foreach>
    </update>

    <update id="increaseContractRemainAmount">
        <foreach collection="list" item="item" separator=";">
            update bid_purchase_request
            set remain_amount=#{item.shareAmount},
            update_time = now(),
            updator_no = #{userId},
            updator = #{userName}
            where form_num=#{item.formNum} and remain_amount=#{item.checkAmount}
        </foreach>
    </update>

    <update id="invalidByFormNum">
        update bid_purchase_request
        set is_valid=0, update_time=now(), updator_no=#{userId}, updator=#{userName}
        where is_valid=1 and form_num=#{formNum}
    </update>

    <update id="decreasePoRemainAmount">
        <foreach collection="list" item="item" separator=";">
            update bid_purchase_request
            set remain_amount_po=#{item.shareAmount},
            update_time = now(),
            updator_no = #{userId},
            updator = #{userName}
            where form_num=#{item.formNum} and remain_amount_po=#{item.checkAmount} and #{item.shareAmount}>=0
        </foreach>
    </update>

    <update id="increasePoRemainAmount">
        <foreach collection="list" item="item" separator=";">
            update bid_purchase_request
            set remain_amount_po=#{item.shareAmount},
            update_time = now(),
            updator_no = #{userId},
            updator = #{userName}
            where form_num=#{item.formNum} and remain_amount_po=#{item.checkAmount}
        </foreach>
    </update>

    <update id="updateCreator">
        update bid_purchase_request
        set creator=#{targetUserName}, creator_no=#{targetUserId}
        where form_num=#{formNum}
    </update>

    <update id="updatePurchaseConfirmAmount">
        update bid_purchase_request
        set confirm_purchase_amount = expected_purchase_amount
        where form_num=#{formNum} and confirm_purchase_amount=0
    </update>

    <select id="searchPurchaseProjectName" resultType="java.lang.String">
        select puchase_project_name
        from bid_purchase_request
        where is_valid=1 and creator_no = #{userId} and puchase_project_name like concat('%',#{name},'%')
        order by id
        limit #{limit}
    </select>

    <select id="queryPurchaseRequestList" parameterType="com.xhs.oa.purchasebid.param.PurchaseRequestParam" resultType="com.xhs.oa.purchasebid.dto.PurchaseRequestDto">
        <include refid="queryPurchaseRequestBaseColumn"/>
        <include refid="queryPurchaseRequestWhere"/>

        union

        <include refid="queryPurchaseRequestBaseColumn"/>
        <include refid="FormShareList"/>


        order by createTime desc
        limit #{start},#{pageSize}
    </select>

    <sql id="FormShareList">
        from t_form_share_auth fsa
        join common_form cf on fsa.form_num = cf.form_num and cf.is_valid=1
        join bid_purchase_request bpr on bpr.form_num=fsa.form_num and bpr.is_valid=1
        left join bid_pr bp on bp.form_num = bpr.form_num
        where fsa.is_valid=1
            and ( (fsa.auth_value=#{shareUserId} and fsa.auth_type='personal')
                <if test="shareDeptIds!=null and shareDeptIds.size()>0">
                    or (fsa.auth_value in
                    <foreach collection="shareDeptIds" item="shareDeptId" separator="," open="(" close=")">
                        #{shareDeptId}
                    </foreach>
                    and fsa.auth_type='department')
                </if>
            )
            <if test="formNum != null and formNum != '' ">
                and bpr.form_num like concat('%',#{formNum},'%')
            </if>

            <if test="formAuditStatus != null and formAuditStatus != '' ">
                and  cf.audit_status = #{formAuditStatus}
            </if>

            <if test="beginAmount != null and endAmount != null">
                <![CDATA[ and bpr.expected_purchase_amount >= #{beginAmount} and bpr.expected_purchase_amount <= #{endAmount} ]]>
            </if>

            <if test="purchaseProjectName != null and purchaseProjectName != '' ">
                and bpr.puchase_project_name like concat('%',#{purchaseProjectName},'%')
            </if>

        <if test="sourcePlatformCode != null and sourcePlatformCode != '' ">
            <choose>
                <when test="sourcePlatformCode=='OA'">
                    <if test="sourcePlatformCode!=null">
                        and (bp.source_platform_code=#{sourcePlatformCode} OR bp.source_platform_code is null)
                    </if>
                </when>
                <otherwise>
                    and bp.source_platform_code=#{sourcePlatformCode}
                </otherwise>
            </choose>
        </if>
    </sql>

    <select id="queryPurchaseRequestListCount"  parameterType="com.xhs.oa.purchasebid.param.PurchaseRequestParam"  resultType="java.lang.Integer">
        select
        count(1)
        from (
            select cf.form_num
            <include refid="queryPurchaseRequestWhere"/>

            union

            select cf.form_num
            <include refid="FormShareList"/>

        ) temp
    </select>

    <sql id="queryPurchaseRequestBaseColumn">
        select
        bpr.id     id,
        bpr.form_num     formNum,
        bpr.puchase_project_name     purchaseProjectName,
        bpr.puchase_item_name     serviceName,
        bpr.expected_start_time     forecasePurchaseBeginTime,
        bpr.expected_purchase_amount  applyForecasePurchaseAmount,
        bpr.confirm_purchase_amount     confirmPurchaseAmount,
        bpr.puchase_purpose     purchasePurpose,
        bpr.source_times  sourceTimes,
        cf.create_time     createTime,
        cf.audit_status   formAuditStatus,
        cf.current_step   currentStep,
        cf.current_audit_user currentAuditMan,
        cf.creator_no  creatorNo,
        cf.be_entrusted_id  beEntrustedId,
        cf.form_type   formType,
        bp.source_platform_code sourcePlatformCode
    </sql>

    <sql id="queryPrBaseColumn">
        select bpr.id                id,
               bpr.form_num          formNum,
               ''                    purchaseProjectName,
               ''                    serviceName,
               bpr.create_time       forecasePurchaseBeginTime,
               0                     applyForecasePurchaseAmount,
               0                     confirmPurchaseAmount,
               ''                    purchasePurpose,
               0                     sourceTimes,
               cf.create_time        createTime,
               cf.audit_status       formAuditStatus,
               cf.current_step       currentStep,
               cf.current_audit_user currentAuditMan,
               cf.creator_no         creatorNo,
               cf.be_entrusted_id    beEntrustedId
    </sql>

    <sql id="queryPurchaseRequestWhere">
        from bid_purchase_request bpr
        join common_form cf on bpr.form_num = cf.form_num
        left join bid_pr bp on bp.form_num = bpr.form_num
        <if test="deptIds!=null">
            join ACT_ID_USER aiu on bpr.creator_no=aiu.ID_
        </if>
        where cf.is_valid = 1 and bpr.is_valid = 1

        <choose>
            <when test="deptIds==null">
                <if test="userId!=null">
                    and (cf.creator_no = #{userId} or cf.be_entrusted_id = #{userId})
                </if>
            </when>
            <otherwise>
                and
                (aiu.DEPARTMENT_ID in
                <foreach collection="deptIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                <if test="userId!=null">
                    or cf.creator_no = #{userId} or cf.be_entrusted_id = #{userId}
                </if>
                )
            </otherwise>
        </choose>

        <if test="formNum != null and formNum != '' ">
            and bpr.form_num like concat('%',#{formNum},'%')
        </if>

        <if test="formAuditStatus != null and formAuditStatus != '' ">
            and  cf.audit_status = #{formAuditStatus}
        </if>

        <if test="beginAmount != null and endAmount != null">
            <![CDATA[ and bpr.expected_purchase_amount >= #{beginAmount} and bpr.expected_purchase_amount <= #{endAmount} ]]>
        </if>

        <if test="purchaseProjectName != null and purchaseProjectName != '' ">
            and bpr.puchase_project_name like concat('%',#{purchaseProjectName},'%')
        </if>

        <if test="sourcePlatformCode != null and sourcePlatformCode != '' ">
            <choose>
                <when test="sourcePlatformCode=='OA'">
                    <if test="sourcePlatformCode!=null">
                        and (bp.source_platform_code=#{sourcePlatformCode} OR bp.source_platform_code is null)
                    </if>
                </when>
                <otherwise>
                       and bp.source_platform_code=#{sourcePlatformCode}
                </otherwise>
            </choose>
        </if>
    </sql>

    <sql id="queryPrWhere">
        from bid_pr bpr
        join common_form cf on bpr.form_num = cf.form_num
        <if test="deptIds!=null">
            join ACT_ID_USER aiu on bpr.creator_no=aiu.ID_
        </if>
        where cf.is_valid = 1 and bpr.is_valid = 1

        <choose>
            <when test="deptIds==null">
                <if test="userId!=null">
                    and (cf.creator_no = #{userId} or cf.be_entrusted_id = #{userId})
                </if>
            </when>
            <otherwise>
                and
                (aiu.DEPARTMENT_ID in
                <foreach collection="deptIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                <if test="userId!=null">
                    or cf.creator_no = #{userId} or cf.be_entrusted_id = #{userId}
                </if>
                )
            </otherwise>
        </choose>

        <if test="formNum != null and formNum != '' ">
            and bpr.form_num like concat('%',#{formNum},'%')
        </if>

        <if test="formAuditStatus != null and formAuditStatus != '' ">
            and  cf.audit_status = #{formAuditStatus}
        </if>

        <if test="beginAmount != null and endAmount != null">
            <![CDATA[ and cf.amount >= #{beginAmount} and cf.amount <= #{endAmount} ]]>
        </if>

        <if test="purchaseProjectName != null and purchaseProjectName != '' ">
            and bpr.id = null
        </if>
    </sql>


    <select id="queryPurchaseRequestListForExport"  parameterType="com.xhs.oa.purchasebid.param.PurchaseRequestParam" resultType="com.xhs.oa.purchasebid.dto.PurchaseRequestDto">
        <include refid="queryPurchaseRequestBaseColumn"/>
        <include refid="queryPurchaseRequestWhere"/>

        union

        <include refid="queryPurchaseRequestBaseColumn"/>
        <include refid="FormShareList"/>

        order by id desc
    </select>

    <select id="queryPurchaseRequestByFormNos" parameterType="java.util.List" resultType="com.xhs.oa.purchasebid.model.BidPurchaseRequest">
        select
        <include refid="BaseColumn"/>
        from bid_purchase_request
        where form_num in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="queryBidPurchaseRequestInfoByFormNo" resultType="com.xhs.oa.purchasebid.model.BidPurchaseRequest">
        select
        <include refid="BaseColumn"/>
        from bid_purchase_request
        where form_num = #{formNo}
    </select>

    <select id="findRemainAmountByFormNum" resultType="java.math.BigDecimal">
        select remain_amount
        from bid_purchase_request
        where form_num=#{formNum}
    </select>

    <select id="mappingQueryRequestFormNums" resultType="java.lang.String">
        select bpr.form_num
        from bid_purchase_request bpr
        join common_form cf on bpr.form_num=cf.form_num
        <if test="deptIds!=null">
            join ACT_ID_USER aiu on bpr.creator_no=aiu.ID_
        </if>
        where cf.is_valid = 1 and cf.audit_status=#{auditStatus}
        <choose>
            <when test="deptIds==null">
                and cf.creator_no = #{userId}
            </when>
            <otherwise>
                and
                (aiu.DEPARTMENT_ID in
                <foreach collection="deptIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                or cf.creator_no = #{userId}
                )
            </otherwise>
        </choose>
        and bpr.form_num like concat('%',#{key},'%')
        order by bpr.id desc
        limit 10
    </select>

    <select id="queryProjectNameBySourceNum" resultType="java.lang.String">
        select distinct puchase_project_name
        from bid_purchase_request bpr
        join bid_request_source_relation brsr on brsr.request_form_num=bpr.form_num and brsr.is_valid=1
        where bpr.is_valid=1 and brsr.source_form_num=#{sourceFormNum}
    </select>

</mapper>   
