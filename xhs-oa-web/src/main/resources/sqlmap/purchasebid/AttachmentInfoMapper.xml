<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.AttachmentInfoMapper" > 

	<sql id="BaseColumn">
          id     id,
          attachment_name     attachmentName,
          attachment_url     attachmentUrl,
          attach_type     attachType,
          attach_reference_id     attachReferenceId,
          creator     creator,
          create_time     createTime
    </sql>

    <delete id="deleteByReferenceIdAndType">
        delete from attachment_info
        where attach_reference_id=#{refId} and attach_type=#{type}
    </delete>

    <delete id="batchDeleteByRefIdAndType">
        <foreach collection="list" item="item" separator=";">
            delete from attachment_info
            where attach_reference_id=#{item.attachReferenceId} and attach_type=#{item.attachType}
        </foreach>
    </delete>

    <delete id="batchDeleteAttachments">
        delete from attachment_info
        where id in
        <foreach collection="list" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.purchasebid.model.AttachmentInfo">
	    select 
	    <include refid="BaseColumn"/>
	    from attachment_info
	    where id = #{id}
	</select>

    <select id="selectByReferenceIdAndType" resultType="com.xhs.oa.purchasebid.model.AttachmentInfo">
        select <include refid="BaseColumn"/>
        from attachment_info
        where attach_reference_id=#{refId} and attach_type=#{type}
    </select>

    <select id="selectByReferenceIdsAndType" resultType="com.xhs.oa.purchasebid.model.AttachmentInfo">
        select <include refid="BaseColumn"/>
        from attachment_info
        where attach_type=#{type} and attach_reference_id in
        <foreach collection="refIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByPrimaryKeys" resultType="com.xhs.oa.purchasebid.model.AttachmentInfo">
        select <include refid="BaseColumn"></include>
        from attachment_info
        where id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>


    <insert id="insert" parameterType="com.xhs.oa.purchasebid.model.AttachmentInfo" useGeneratedKeys="true" keyProperty="id">
    insert into attachment_info (
          attachment_name,
          attachment_url,
          attach_type,
          attach_reference_id,
          creator,
          create_time
      )
    values (
           #{attachmentName},
           #{attachmentUrl},
           #{attachType},
           #{attachReferenceId},
           #{creator},
           now()
      )
  </insert>

    <insert id="batchInsertAttachmentInfoList" parameterType="java.util.List" >
        insert into attachment_info (
          attachment_name,
          attachment_url,
          attach_type,
          attach_reference_id,
          creator,
          create_time
      )
    values
    <foreach collection="list" item="item" separator=",">
    (
           #{item.attachmentName},
           #{item.attachmentUrl},
           #{item.attachType},
           #{item.attachReferenceId},
           #{item.creator},
           now()
      )
    </foreach>
    </insert>

  
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.AttachmentInfo">
    update attachment_info
    set 
           id = #{id},

           attachment_name = #{attachmentName},

           attachment_url = #{attachmentUrl},

           attach_type = #{attachType},

           attach_reference_id = #{attachReferenceId},

           creator = #{creator},

         create_time = #{createTime}
    where id = #{id}
  </update>

    <update id="modifyRefId">
        <foreach collection="list" item="item" separator=";">
            update attachment_info
            set attach_reference_id=#{item.newRdfId}
            where attach_reference_id=#{item.oldRefId} and attach_type=#{item.refType}
        </foreach>
    </update>

    <update id="updateByReferenceIdAndType">
        update attachment_info
        set attachment_name = #{attachmentName},
            attachment_url = #{attachmentUrl}
        where attach_reference_id=#{refId} and attach_type=#{type}
    </update>

</mapper>   
