<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.BidPsRelationMapper" >

	<sql id="BaseColumn">
          id     id,
          ps_form_num     psFormNum,
          relation_form_num     relationFormNum,
          relation_type     relationType,
          is_valid     isValid,
          create_time     createTime,
          creator_no     creatorNo,
          update_time     updateTime,
          updator_no     updatorNo
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.purchasebid.model.BidPsRelation">
	    select 
	    <include refid="BaseColumn"/>
	    from bid_ps_relation
	    where id = #{id}
	</select>


  <insert id="insert" parameterType="com.xhs.oa.purchasebid.model.BidPsRelation">
    insert into bid_ps_relation (
          ps_form_num,
          relation_form_num,
          relation_type,
          is_valid,
          create_time,
          creator_no,
          update_time,
          updator_no
      )
    values (
           #{psFormNum},
           #{relationFormNum},
           #{relationType},
           1,
           now(),
           #{creatorNo},
           now(),
          #{updatorNo}
      )
  </insert>


  <insert id="batchInsertRelationData" parameterType="java.util.List">
      insert into bid_ps_relation (
          ps_form_num,
          relation_form_num,
          relation_type,
          is_valid,
          create_time,
          creator_no,
          update_time,
          updator_no
      )
    values
    <foreach collection="list" item="item" separator=",">
    (
       #{item.psFormNum},
       #{item.relationFormNum},
       #{item.relationType},
       1,
       now(),
       #{item.creatorNo},
       now(),
       #{item.updatorNo}
    )
    </foreach>
  </insert>

  
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidPsRelation">
    update bid_ps_relation
    set 
           id = #{id},

           ps_form_num = #{psFormNum},

           relation_form_num = #{relationFormNum},

           relation_type = #{relationType},

           is_valid = #{isValid},

           create_time = #{createTime},

           creator_no = #{creatorNo},

           update_time = #{updateTime},

         updator_no = #{updatorNo}
    where id = #{id}
  </update>


    <select id="queryRelationDataByPsFormNumAndType" resultType="com.xhs.oa.purchasebid.model.BidPsRelation">
        select
        <include refid="BaseColumn"/>
        from bid_ps_relation
        where
        ps_form_num = #{psFormNum}
        and is_valid = 1
        <if test="relationType != null and relationType != ''">
            and relation_type = #{relationType}
        </if>
    </select>

    <select id="batchQueryRelationDataByPsFormNumAndType" resultType="com.xhs.oa.purchasebid.model.BidPsRelation">
        select
        <include refid="BaseColumn"/>
        from bid_ps_relation
        where
        ps_form_num in
        <foreach collection="psFormNums" item="psFormNum" open="(" close=")" separator=",">
            #{psFormNum}
        </foreach>
        and is_valid = 1
        <if test="relationType != null and relationType != ''">
            and relation_type = #{relationType}
        </if>
    </select>


    <select id="queryRelationDataByRelationTypeAndNum" resultType="java.lang.String">
        select
        ps_form_num
        from bid_ps_relation
        where is_valid = 1
        and relation_type = #{relationType}
        and relation_form_num = #{relationFormNum}
        limit 1
    </select>

    <update id="updateInvalidByIds" parameterType="java.util.List">
        update bid_ps_relation
        set is_valid = 0 ,
        update_time = now()
        where id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>

    </update>

    <update id="invalidByPsFormNum" >
        update bid_ps_relation
        set is_valid = 0,
            update_time = now(),
            updator_no = #{updatorNo}
        where ps_form_num = #{psFormNum}
    </update>

    <update id="invalidByRelationFormNum">
        update bid_ps_relation
        set is_valid = 0,
            update_time = now(),
            updator_no = #{updatorNo}
        where relation_form_num = #{relationFormNum}
    </update>
</mapper>   
