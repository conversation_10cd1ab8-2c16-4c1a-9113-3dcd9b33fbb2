<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.purchasebid.mapper.BidContractPrRelationMapper" > 

	<sql id="BaseColumn">
          id     id,
          contract_id     contractId,
          pr_form_num     prFormNum,
          source_form_num  sourceFormNum,
          share_amount  shareAmount,
          is_valid     isValid,
          creator_no     creator<PERSON><PERSON>,
          creator     creator,
          create_time     createTime,
          update_time     updateTime
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.purchasebid.model.BidContractPrRelation">
	    select 
	    <include refid="BaseColumn"/>
	    from bid_contract_pr_relation
	    where id = #{id}
	</select>

    <select id="queryPrShareAmount" resultType="com.xhs.oa.purchasebid.dto.ContractPrDto">
        select
          bcpr.pr_form_num prFormNum,
          bc.contract_form_num contractFormNum,
          bcpr.share_amount shareAmount
        from bid_contract_pr_relation bcpr
        join bid_contract bc on bcpr.contract_id=bc.id
        where bc.is_valid=1 and bcpr.is_valid=1 and bcpr.pr_form_num in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryShareAmountByFormNums" resultType="java.math.BigDecimal">
        select bcpr.share_amount
        from bid_contract bc
        join bid_contract_pr_relation bcpr on bcpr.contract_id=bc.id and bcpr.is_valid=1
        where bc.is_valid=1 and bc.contract_form_num=#{contractFormNum} and bcpr.pr_form_num=#{prFormNum}
    </select>

    <select id="querySourceContractFormRelations" resultType="com.xhs.oa.purchasebid.dto.SourceContractDTO">
        select
            bcpr.source_form_num  sourceFormNum,
            bc.contract_form_num  contractFormNum,
            bc.contract_name  contractName,
            bc.creator_no  contractCreatorNo,
            bc.creator  contractCreator,
            bc.contract_amount  contractAmount
        from bid_contract_pr_relation bcpr
        join bid_contract bc on bcpr.contract_id=bc.id and bc.is_valid=1
        where bcpr.is_valid=1 and bcpr.source_form_num in
        <foreach collection="list" item="sourceNum" separator="," open="(" close=")">
            #{sourceNum}
        </foreach>
    </select>

    <select id="queryByContractIds" resultType="com.xhs.oa.purchasebid.model.BidContractPrRelation">
        select
            <include refid="BaseColumn"></include>
        from bid_contract_pr_relation
        where is_valid=1 and contract_id in
        <foreach collection="list" item="contractId" separator="," open="(" close=")">
            #{contractId}
        </foreach>
    </select>

    <select id="queryContractBySupplierNameAndSourceFormNums" resultType="com.xhs.oa.form.model.CommonForm">
        select
            cf.id  id,
            cf.form_num  formNum,
            cf.form_type  formType,
            cf.audit_status  auditStatus,
            cf.creator  creator,
            cf.creator_no  creatorNo,
            cf.create_time  createTime,
            cf.update_time  updateTime,
            cf.form_content  formContent
        from bid_contract_pr_relation bcpr
        join bid_contract bc on bcpr.contract_id=bc.id and bc.is_valid=1
        join common_form cf on bc.contract_form_num=cf.form_num and cf.is_valid=1
        where bcpr.source_form_num in
        <foreach collection="sourceFormNums" item="sourceFormNum" separator="," open="(" close=")">
            #{sourceFormNum}
        </foreach>
        and cf.form_content like concat('%', #{supplierName}, '%')
        order by bc.id desc
    </select>


    <insert id="insert" parameterType="com.xhs.oa.purchasebid.model.BidContractPrRelation">
    insert into bid_contract_pr_relation (
          id,
          contract_id,
          pr_form_num,
          source_form_num,
          share_amount,
          is_valid,
          creator_no,
          creator,
          create_time,
          update_time
      )
    values (
           #{id},
           #{contractId},
           #{prFormNum},
           #{sourceFormNum},
           #{shareAmount},
           #{isValid},
           #{creatorNo},
           #{creator},
           #{createTime},
          #{updateTime}
      )
  </insert>

    <insert id="batchInsert">
    insert into bid_contract_pr_relation (
          contract_id,
          pr_form_num,
          source_form_num,
          share_amount,
          is_valid,
          creator_no,
          creator,
          create_time,
          update_time
      )
    values
    <foreach collection="list" item="item" separator=",">
        (
        #{item.contractId},
        #{item.prFormNum},
        #{item.sourceFormNum},
        #{item.shareAmount},
        1,
        #{item.creatorNo},
        #{item.creator},
        now(),
        now()
        )
    </foreach>
    </insert>


    <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.BidContractPrRelation">
    update bid_contract_pr_relation
    set 
           id = #{id},

           contract_id = #{contractId},

           pr_form_num = #{prFormNum},

           is_valid = #{isValid},

           creator_no = #{creatorNo},

           creator = #{creator},

           create_time = #{createTime},

         update_time = #{updateTime}
    where id = #{id}
  </update>

    <update id="invalidateByContractId">
        update bid_contract_pr_relation set is_valid=0, update_time=now()
        where is_valid=1 and contract_id=#{contractId}
    </update>

</mapper>   
