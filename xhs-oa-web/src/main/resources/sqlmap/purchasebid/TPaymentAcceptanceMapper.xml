<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhs.oa.purchasebid.mapper.TPaymentAcceptanceMapper">
  <resultMap id="BaseResultMap" type="com.xhs.oa.purchasebid.model.TPaymentAcceptance">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="po_form_num" jdbcType="VARCHAR" property="poFormNum" />
    <result column="fk_form_num" jdbcType="VARCHAR" property="fkFormNum" />
    <result column="ys_form_num" jdbcType="VARCHAR" property="ysFormNum" />
    <result column="pay_amount" jdbcType="DECIMAL" property="payAmount" />
    <result column="is_valid" jdbcType="TINYINT" property="isValid" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, po_form_num, fk_form_num, ys_form_num, pay_amount, is_valid, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_payment_acceptance
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_payment_acceptance
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.xhs.oa.purchasebid.model.TPaymentAcceptance" useGeneratedKeys="true">
    insert into t_payment_acceptance (po_form_num, fk_form_num, ys_form_num, 
      pay_amount, is_valid, create_time, 
      update_time)
    values (#{poFormNum,jdbcType=VARCHAR}, #{fkFormNum,jdbcType=VARCHAR}, #{ysFormNum,jdbcType=VARCHAR}, 
      #{payAmount,jdbcType=DECIMAL}, #{isValid,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.xhs.oa.purchasebid.model.TPaymentAcceptance" useGeneratedKeys="true">
    insert into t_payment_acceptance
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="poFormNum != null">
        po_form_num,
      </if>
      <if test="fkFormNum != null">
        fk_form_num,
      </if>
      <if test="ysFormNum != null">
        ys_form_num,
      </if>
      <if test="payAmount != null">
        pay_amount,
      </if>
      <if test="isValid != null">
        is_valid,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="poFormNum != null">
        #{poFormNum,jdbcType=VARCHAR},
      </if>
      <if test="fkFormNum != null">
        #{fkFormNum,jdbcType=VARCHAR},
      </if>
      <if test="ysFormNum != null">
        #{ysFormNum,jdbcType=VARCHAR},
      </if>
      <if test="payAmount != null">
        #{payAmount,jdbcType=DECIMAL},
      </if>
      <if test="isValid != null">
        #{isValid,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhs.oa.purchasebid.model.TPaymentAcceptance">
    update t_payment_acceptance
    <set>
      <if test="poFormNum != null">
        po_form_num = #{poFormNum,jdbcType=VARCHAR},
      </if>
      <if test="fkFormNum != null">
        fk_form_num = #{fkFormNum,jdbcType=VARCHAR},
      </if>
      <if test="ysFormNum != null">
        ys_form_num = #{ysFormNum,jdbcType=VARCHAR},
      </if>
      <if test="payAmount != null">
        pay_amount = #{payAmount,jdbcType=DECIMAL},
      </if>
      <if test="isValid != null">
        is_valid = #{isValid,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.purchasebid.model.TPaymentAcceptance">
    update t_payment_acceptance
    set po_form_num = #{poFormNum,jdbcType=VARCHAR},
      fk_form_num = #{fkFormNum,jdbcType=VARCHAR},
      ys_form_num = #{ysFormNum,jdbcType=VARCHAR},
      pay_amount = #{payAmount,jdbcType=DECIMAL},
      is_valid = #{isValid,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryYsFormNumByFkFormNum" parameterType="string" resultType="string">
    select
      ys_form_num
    from t_payment_acceptance
    where fk_form_num = #{fkFormNum} and is_valid =1 order by create_time desc limit 1
  </select>
</mapper>