<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.commonConfig.mapper.CommonConfigMapper" >

    <sql id="BaseColumn">
        id     id,
        type     type,
        code     code,
        name     name,
        sort_num sortNum,
        is_deleted isDeleted
    </sql>

    <insert id="addCommonConfig" parameterType="com.xhs.oa.commonConfig.model.CommonConfig">
        insert into common_config(
          type,
          code,
          name,
          sort_num,
          is_deleted,
          create_time,
          update_time,
          creator_no,
          creator,
          updator_no,
          updator
        ) values (
          #{type},
          #{code},
          #{name},
          #{sortNum},
          0,
          now(),
          now(),
          #{creatorNo},
          #{creator},
          #{updatorNo},
          #{updator}
        )
    </insert>

    <delete id="deleteCommonConfig">
        delete from common_config where id=#{configId}
    </delete>

    <select id="getCommonConfigByType" parameterType="java.lang.String" resultType="com.xhs.oa.commonConfig.model.CommonConfig">
        select
        <include refid="BaseColumn"/>
        from common_config
        where type = #{VALUE} order by sort_num
    </select>

    <update id="updateCommonConfigById" parameterType="com.xhs.oa.commonConfig.param.CommonConfigParm">
        update common_config
            set
                code = #{param.code},
                name = #{param.name},
                updator_no = #{userId},
                updator = #{userName},
                update_time = now()
             where id = #{param.id}
    </update>

    <update id="updateNameByTypeAndCode">
        update common_config
            set
                name = #{name},
                updator_no = #{userId},
                updator = #{userName},
                update_time = now()
             where type=#{type} and code=#{code} and name=#{oldName}
    </update>

</mapper>
