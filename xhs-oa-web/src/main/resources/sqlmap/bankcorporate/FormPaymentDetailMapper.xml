<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.bankcorporate.mapper.FormPaymentDetailMapper" > 

	<sql id="BaseColumn">
          id     id,
          payment_no     paymentNo,
          form_no     formNo,
          gathering_detail_no     gatheringDetailNo,
          payment_status     paymentStatus,
          amount     amount,
          gathering_name     gatheringName,
          subject     subject,
          priate_or_public     priateOrPublic,
          gathering_account     gatheringAccount,
          bank_name     bankName,
          bank_code     bankCode,
          is_oversea    isOversea,
          payment_currency     paymentCurrency,
          hw_bank_code_type     hwBankCodeType,
          hw_bank_code     hwBankCode,
          contact_phone     contactPhone,
          payment_success_time paymentSuccessTime,
          remark          remark,
          reason          reason,
          create_time     createTime,
          update_time     updateTime,
          creator_no     creator<PERSON><PERSON>,
          creator     creator,
          updator_no     updatorNo,
          updator     updator
    </sql>

    <sql id="JoinP1BaseColumn">
        p1.id     id,
        p1.payment_no     paymentNo,
        p1.form_no     formNo,
        p1.gathering_detail_no     gatheringDetailNo,
        p1.payment_status     paymentStatus,
           p1.amount     amount,
           p1.gathering_name     gatheringName,
           p1.subject     subject,
           p1.priate_or_public     priateOrPublic,
           p1.gathering_account     gatheringAccount,
           p1.bank_name     bankName,
           p1.bank_code     bankCode,
           p1.is_oversea    isOversea,
           p1.payment_currency     paymentCurrency,
           p1.hw_bank_code_type     hwBankCodeType,
           p1.hw_bank_code     hwBankCode,
           p1.contact_phone     contactPhone,
           p1.payment_success_time paymentSuccessTime,
           p1.remark          remark,
           p1.reason          reason,
           p1.create_time     createTime,
           p1.update_time     updateTime,
           p1.creator_no     creatorNo,
           p1.creator     creator,
           p1.updator_no     updatorNo,
           p1.updator     updator
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.bankcorporate.model.FormPaymentDetail">
	    select 
	    <include refid="BaseColumn"/>
	    from form_payment_detail
	    where id = #{id} and is_valid = 1
	</select>


  <insert id="insert" parameterType="com.xhs.oa.bankcorporate.model.FormPaymentDetail">
    insert into form_payment_detail (
          id,
          payment_no,
          form_no,
          gathering_detail_no,
          payment_status,
          amount,
          gathering_name,
          subject,
          priate_or_public,
          gathering_account,
          bank_name,
          bank_code,
          is_oversea,
          payment_currency,
          hw_bank_code_type,
          hw_bank_code,
          contact_phone,
          remark,
          reason,
          create_time,
          update_time,
          creator_no,
          creator,
          updator_no,
          updator
      )
    values (
           #{id},
           #{paymentNo},
           #{formNo},
           #{gatheringDetailNo},
           #{paymentStatus},
           #{amount},
           #{gatheringName},
           #{subject},
           #{priateOrPublic},
           #{gatheringAccount},
           #{bankName},
           #{bankCode},
           #{isOversea},
           #{paymentCurrency},
           #{hwBankCodeType},
           #{hwBankCode},
           #{contactPhone},
           #{remark},
           #{reason},
           #{createTime},
           #{updateTime},
           #{creatorNo},
           #{creator},
           #{updatorNo},
          #{updator}
      )
  </insert>

    <insert id="batchInsertPaymentDetail" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
      insert into form_payment_detail (
          payment_no,
          form_no,
          gathering_detail_no,
          payment_status,
          amount,
          gathering_name,
          subject,
          priate_or_public,
          gathering_account,
          bank_name,
          bank_code,
          is_oversea,
          payment_currency,
          hw_bank_code_type,
          hw_bank_code,
          contact_phone,
          remark,
          reason,
          sub_beneficiary_account_type,
          cross_border,
          payment_detail_remark,
          create_time,
          update_time,
          creator_no,
          creator,
          updator_no,
          updator
      )
    values
    <foreach collection="list" item="item" separator=",">
    (
           #{item.paymentNo},
           #{item.formNo},
           #{item.gatheringDetailNo},
           #{item.paymentStatus},
           #{item.amount},
           #{item.gatheringName},
           #{item.subject},
           #{item.priateOrPublic},
           #{item.gatheringAccount},
           #{item.bankName},
           #{item.bankCode},
           #{item.isOversea},
           #{item.paymentCurrency},
           #{item.hwBankCodeType},
           #{item.hwBankCode},
           #{item.contactPhone},
           #{item.remark},
           #{item.reason},
           #{item.beneficiaryAccountType},
           #{item.crossBorder},
           #{item.paymentDetailRemark},
           now(),
           now(),
           #{item.creatorNo},
           #{item.creator},
           #{item.updatorNo},
           #{item.updator}
      )
    </foreach>
    </insert>


    <update id="updatePayResultByPaymentNo" >
        update form_payment_detail
        set  payment_status = #{payStatus},
             updator_no = #{updatorNo},
             updator = #{updator},
            <if test="remark != null and remark != ''">
               remark = #{remark} ,
            </if>
            <if test="reason != null and reason != '' ">
                reason = #{reason},
            </if>
             update_time = now()
        where payment_no = #{paymentNo}
    </update>


    <update id="batchUpdatePaymentNoByID" parameterType="com.xhs.oa.bankcorporate.model.FormPaymentDetail">
         <foreach collection="list" item="item" open="" close="" separator=";">
             update form_payment_detail
             set payment_no = #{item.paymentNo},
             updator_no = #{item.updatorNo},
             updator = #{item.updator},
             update_time = now()
             where id = #{item.id}
         </foreach>
    </update>

  <update id="updateByPrimaryKeySelective" parameterType="com.xhs.oa.bankcorporate.model.FormPaymentDetail">
    update form_payment_detail
    <set>
        <if test="id != null">
           id = #{id},
        </if>
        <if test="paymentNo != null">
           payment_no = #{paymentNo},
        </if>
        <if test="formNo != null">
           form_no = #{formNo},
        </if>
        <if test="gatheringDetailNo != null">
           gathering_detail_no = #{gatheringDetailNo},
        </if>
        <if test="paymentStatus != null">
           payment_status = #{paymentStatus},
        </if>
        <if test="amount != null">
           amount = #{amount},
        </if>
        <if test="gatheringName != null">
           gathering_name = #{gatheringName},
        </if>
        <if test="subject != null">
           subject = #{subject},
        </if>
        <if test="priateOrPublic != null">
           priate_or_public = #{priateOrPublic},
        </if>
        <if test="gatheringAccount != null">
           gathering_account = #{gatheringAccount},
        </if>
        <if test="bankName != null">
           bank_name = #{bankName},
        </if>
        <if test="bankCode != null">
           bank_code = #{bankCode},
        </if>
        <if test="isOversea != null">
            is_oversea = #{isOversea},
        </if>
        <if test="paymentCurrency != null">
           payment_currency = #{paymentCurrency},
        </if>
        <if test="hwBankCodeType != null">
           hw_bank_code_type = #{hwBankCodeType},
        </if>
        <if test="hwBankCode != null">
           hw_bank_code = #{hwBankCode},
        </if>
        <if test="contactPhone != null">
           contact_phone = #{contactPhone},
        </if>
        <if test="remark != null">
            remark = #{remark},
        </if>
        <if test="reason != null">
            reason = #{reason},
        </if>
        <if test="createTime != null">
           create_time = #{createTime},
        </if>
        <if test="updateTime != null">
           update_time = #{updateTime},
        </if>
        <if test="creatorNo != null">
           creator_no = #{creatorNo},
        </if>
        <if test="creator != null">
           creator = #{creator},
        </if>
        <if test="updatorNo != null">
           updator_no = #{updatorNo},
        </if>
        <if test="updator != null">
           updator = #{updator},
        </if>
    </set>
    where id = #{id}
  </update>
  
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.bankcorporate.model.FormPaymentDetail">
    update form_payment_detail
    set 
           id = #{id},

           payment_no = #{paymentNo},

           form_no = #{formNo},

           gathering_detail_no = #{gatheringDetailNo},

           payment_status = #{paymentStatus},

           amount = #{amount},

           gathering_name = #{gatheringName},

           subject = #{subject},

           priate_or_public = #{priateOrPublic},

           gathering_account = #{gatheringAccount},

           bank_name = #{bankName},

           bank_code = #{bankCode},

           is_oversea = #{isOversea},

           payment_currency = #{paymentCurrency},

           hw_bank_code_type = #{hwBankCodeType},

           hw_bank_code = #{hwBankCode},

           contact_phone = #{contactPhone},

           remark = #{remark},

           reason = #{reason},

           create_time = #{createTime},

           update_time = #{updateTime},

           creator_no = #{creatorNo},

           creator = #{creator},

           updator_no = #{updatorNo},

         updator = #{updator}
    where id = #{id}
  </update>

   <select id="getFormDetailLastPaymentInfo" parameterType="java.lang.String" resultType="com.xhs.oa.bankcorporate.model.FormPaymentDetail">
        select
       <include refid="BaseColumn"/>
        from
        form_payment_detail where id in (
            select max(id)  from form_payment_detail where form_no = #{formNum} and is_valid = 1 group by gathering_detail_no
          )
       order by id desc
   </select>

    <select id="batchQueryFormDetailLastPaymentInfo" parameterType="java.util.List" resultType="com.xhs.oa.bankcorporate.model.FormPaymentDetail">
        select
        <include refid="BaseColumn"/>
        from
        form_payment_detail where id in (
        select max(id)  from form_payment_detail where
        form_no IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach> and is_valid = 1
        group by gathering_detail_no
        )
    </select>

    <select id="queryPaymentDetailWithStatusAndDate" resultType="com.xhs.oa.bankcorporate.model.FormPaymentDetail">
        <![CDATA[
        select payment_no as paymentNo,
        amount
        from form_payment_detail
        where payment_status = #{paymentStatus}
        and update_time>=#{startDate} and update_time<=#{endDate} and is_valid = 1

        ]]>
    </select>

    <select id="queryByPaymentNo" parameterType="java.lang.String" resultType="com.xhs.oa.bankcorporate.model.FormPaymentDetail">
        select
        <include refid="BaseColumn"/>
        from form_payment_detail
        where payment_no = #{paymentNo} and is_valid = 1
    </select>

    <update id="updatePaymentStatus">
        update form_payment_detail
        set payment_status = #{targetStatus},
        remark = #{remark},
        reason = #{reason},
        update_time = sysdate()
        <if test="paymentSuccessTime != null">
            ,payment_success_time = #{paymentSuccessTime}
        </if>
        where id = #{id} and payment_status=#{currStatus}
    </update>

    <select id="queryByFormNum" parameterType="java.lang.String" resultType="com.xhs.oa.bankcorporate.model.FormPaymentDetail">
        select
        <include refid="BaseColumn"/>
        from form_payment_detail
        where form_no = #{formNo} and is_valid = 1
    </select>

    <select id="queryRecordInfoByPaymentNos" parameterType="list" resultType="com.xhs.oa.bankcorporate.model.FormPaymentDetail">
        select <include refid="BaseColumn"/>
        from form_payment_detail
        where payment_no in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach> and is_valid = 1
    </select>

    <select id="findPaymentDetailByParam" resultType="com.xhs.oa.bankcorporate.dto.ApplyPaymentGatherDTO">
        select
        form_no            formNum,
        sum(amount)         payAmount,
        max(subject)             payCompanyName,
        max(payment_currency)    currencyCode,
        count(1)            recordCount
        from form_payment_detail
        where
        payment_status in
        <foreach collection="payResultList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and form_no in(
        <foreach collection="formNumList" item="item"  separator=",">
            #{item}
        </foreach>
        ) and is_valid = 1
        group by form_no
    </select>

    <select id="batchFindFormDetailByParam" resultType="com.xhs.oa.bankcorporate.dto.FormDetailPayDTO">
        select
            form_no             formNum,
            sum(amount)         sumAmount,
            max(update_time)    updateTime
        from form_payment_detail
        where
            form_no in
            <foreach collection="formNumList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            and  payment_status in
            <foreach collection="payResultList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach> and is_valid = 1
        group by form_no
    </select>

    <select id="batchQueryFormDetailPaymentInfo" resultType="com.xhs.oa.bankcorporate.model.FormPaymentDetail">
        select
        <include refid="BaseColumn"/>
        from form_payment_detail
        where form_no in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach> and is_valid = 1
    </select>

    <select id="selectPaymentSuccessByFormTypeAndYesterday"
            resultType="com.xhs.oa.bankcorporate.model.FormPaymentDetail" parameterType="com.xhs.oa.bankcorporate.param.PaymentSuccessParam">
        select <include refid="JoinP1BaseColumn"></include>
        from form_payment_detail p1 left join common_form cf on p1.form_no = cf.form_num
        where p1.payment_status = #{paymentStatus} and p1.is_valid = 1
        and p1.update_time between #{paymentTime} and date_add(#{paymentTime},interval 1 day)
        and cf.form_type in
        <foreach collection="formTypeList" item="type" open="(" close=")" separator=",">
            #{type}
        </foreach>
        and cf.is_valid = 1
        order by p1.update_time desc
    </select>

    <select id="selectSuccessDetailByFormNumList"
            resultType="com.xhs.oa.bankcorporate.model.FormPaymentDetail">
        select
        <include refid="BaseColumn"/>
        from form_payment_detail
        where payment_status = #{paymentStatus}
        and form_no in
        <foreach collection="formNumList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and is_valid = 1
    </select>

    <select id="selectByStatusOnTime" resultType="java.lang.String">
        select
          form_no
        from form_payment_detail
        where payment_status = #{payStatus} and is_valid = 1
        and form_no like concat(#{formType},'%')
        and payment_success_time between #{startTime} and #{endTime}
    </select>

    <select id="selectDeatilByFormNoAndStatus" resultType="com.xhs.oa.bankcorporate.model.FormPaymentDetail">
        select
        <include refid="BaseColumn"/>
        from form_payment_detail
        where payment_status = #{status} and is_valid = 1
        and form_no = #{formNum}
        order by id desc
    </select>

    <select id="queryLastDetailByFormNum" resultType="com.xhs.oa.bankcorporate.model.FormPaymentDetail">
        select
        <include refid="BaseColumn"/>
        from form_payment_detail
        where form_no = #{formNum}
        and is_valid = 1
        order by id desc limit 1
    </select>

    <update id="updatePaymentSuccessTime" >
        update form_payment_detail set payment_success_time = update_time where payment_status = 'PAY_SUCCESS'
    </update>

    <delete id="deletePaymentByFK">
        DELETE FROM form_payment_detail WHERE form_no = #{formNum}
    </delete>

    <update id="invalidFormPaymentDetail">
        update form_payment_detail set is_valid = 0 WHERE form_no = #{formNum}
    </update>

</mapper>
