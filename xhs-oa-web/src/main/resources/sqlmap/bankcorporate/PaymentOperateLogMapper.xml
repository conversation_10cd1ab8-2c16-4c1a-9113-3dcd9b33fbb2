<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.bankcorporate.mapper.PaymentOperateLogMapper" > 

	<sql id="BaseColumn">
          id     id,
          operate_type     operateType,
          form_no     formNo,
          remark     remark,
          operator     operator,
          operatorNo     operatorno,
          operator_time     operatorTime
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.bankcorporate.model.PaymentOperateLog">
	    select 
	    <include refid="BaseColumn"/>
	    from payment_operate_log
	    where id = #{id}
	</select>


  <insert id="insert" parameterType="com.xhs.oa.bankcorporate.model.PaymentOperateLog">
    insert into payment_operate_log (
          operate_type,
          form_no,
          remark,
          operator,
          operatorNo,
          operator_time
      )
    values (
           #{operateType},
           #{formNo},
           #{remark},
           #{operator},
           #{operatorno},
          now()
      )
  </insert>
    
    <insert id="batchInsertLog" parameterType="java.util.List">
        insert into payment_operate_log (
          operate_type,
          form_no,
          remark,
          operator,
          operatorNo,
          operator_time
      )
    values
        <foreach collection="list" item="item" separator=",">
        (
           #{item.operateType},
           #{item.formNo},
           #{item.remark},
           #{item.operator},
           #{item.operatorno},
          now()
      )
        </foreach>
    </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.xhs.oa.bankcorporate.model.PaymentOperateLog">
    update payment_operate_log
    <set>
        <if test="id != null">
           id = #{id},
        </if>
        <if test="operateType != null">
           operate_type = #{operateType},
        </if>
        <if test="formNo != null">
           form_no = #{formNo},
        </if>
        <if test="remark != null">
           remark = #{remark},
        </if>
        <if test="operator != null">
           operator = #{operator},
        </if>
        <if test="operatorno != null">
           operatorNo = #{operatorno},
        </if>
        <if test="operatorTime != null">
           operator_time = #{operatorTime},
        </if>
    </set>
    where id = #{id}
  </update>
  
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.bankcorporate.model.PaymentOperateLog">
    update payment_operate_log
    set 
          id = #{id},

          operate_type = #{operateType},

          form_no = #{formNo},

          remark = #{remark},

          operator = #{operator},

          operatorNo = #{operatorno},

         operator_time = #{operatorTime}
    where id = #{id}
  </update>

    <select id="queryPaymentLogByFormNum" resultType="com.xhs.oa.bankcorporate.model.PaymentOperateLog">
        select <include refid="BaseColumn"/>
        from payment_operate_log
        where form_no = #{formNum}
        order by id desc
    </select>

    <select id="getLastOperateLogByTypeAndFormNum" resultType="com.xhs.oa.bankcorporate.model.PaymentOperateLog">
        select
        <include refid="BaseColumn"/>
        from payment_operate_log
        where form_no = #{formNum}
        <if test="operateType != null">
           and  operate_type = #{operateType}
        </if>
        order by id desc limit 1
    </select>

    <select id="batchGetOperateLogByFormNumList" resultType="com.xhs.oa.bankcorporate.model.PaymentOperateLog">
        select
        <include refid="BaseColumn"/>
        from payment_operate_log
        where form_no in
                (
                <foreach collection="formNumList" item="formNum" separator=",">
                    #{formNum}
                </foreach>
                )
              and  operate_type = #{operateType}
    </select>

</mapper>
