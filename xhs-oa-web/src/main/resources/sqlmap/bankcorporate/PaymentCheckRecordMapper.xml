<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.bankcorporate.mapper.PaymentCheckRecordMapper" > 

	<sql id="BaseColumn">
          id     id,
          payment_no     paymentNo,
          batch_no       batchNo,
          reason     reason,
          remark     remark,
          check_start_time checkStartTime,
          check_end_time  checkEndTime,
          create_time     createTime,
          update_time     updateTime,
          creator_no     creatorNo,
          creator     creator,
          updator_no     updatorNo,
          updator     updator,
          status     status
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.bankcorporate.model.PaymentCheckRecord">
	    select 
	    <include refid="BaseColumn"/>
	    from payment_check_record
	    where id = #{id}
	</select>


  <insert id="insert" parameterType="com.xhs.oa.bankcorporate.model.PaymentCheckRecord">
    insert into payment_check_record (
          id,
          payment_no,
          batch_no,
          reason,
          remark,
          check_start_time,
          check_end_time,
          create_time,
          update_time,
          creator_no,
          creator,
          updator_no,
          updator,
          status
      )
    values (
           #{id},
           #{paymentNo},
           #{batchNo},
           #{reason},
           #{remark},
           #{checkStartTime},
           #{checkEndTime},
           #{createTime},
           #{updateTime},
           #{creatorNo},
           #{creator},
           #{updatorNo},
           #{updator},
          #{status}
      )
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.xhs.oa.bankcorporate.model.PaymentCheckRecord">
    update payment_check_record
    <set>
        <if test="id != null">
           id = #{id},
        </if>
        <if test="paymentNo != null">
           payment_no = #{paymentNo},
        </if>
        <if test="batchNo != null">
            batch_no = #{batchNo},
        </if>
        <if test="reason != null">
           reason = #{reason},
        </if>
        <if test="remark != null">
           remark = #{remark},
        </if>

        <if test="checkStartTime != null">
            check_start_time = #{checkStartTime},
        </if>
        <if test="checkEndTime != null">
            check_end_time = #{checkEndTime},
        </if>

        <if test="createTime != null">
           create_time = #{createTime},
        </if>
        <if test="updateTime != null">
           update_time = #{updateTime},
        </if>
        <if test="creatorNo != null">
           creator_no = #{creatorNo},
        </if>
        <if test="creator != null">
           creator = #{creator},
        </if>
        <if test="updatorNo != null">
           updator_no = #{updatorNo},
        </if>
        <if test="updator != null">
           updator = #{updator},
        </if>
        <if test="status != null">
           status = #{status},
        </if>
    </set>
    where id = #{id}
  </update>
  
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.bankcorporate.model.PaymentCheckRecord">
    update payment_check_record
    set 
           id = #{id},

           payment_no = #{paymentNo},

           batch_no =#{batchNo},

           reason = #{reason},

           remark = #{remark},

           check_start_time = #{checkStartTime},

           check_end_time = #{checkEndTime},

           create_time = #{createTime},

           update_time = #{updateTime},

           creator_no = #{creatorNo},

           creator = #{creator},

           updator_no = #{updatorNo},

           updator = #{updator},

         status = #{status}
    where id = #{id}
  </update>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into payment_check_record (
        payment_no,
        batch_no,
        reason,
        remark,
        check_start_time,
        check_end_time,
        status
        )values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.paymentNo},
            #{item.batchNo},
            #{item.reason},
            #{item.remark},
            #{item.checkStartTime},
            #{item.checkEndTime},
            #{item.status}
            )
        </foreach>
    </insert>

</mapper>
