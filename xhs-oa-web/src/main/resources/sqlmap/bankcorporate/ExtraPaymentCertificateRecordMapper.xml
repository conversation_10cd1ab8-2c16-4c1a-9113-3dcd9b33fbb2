<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhs.oa.bankcorporate.mapper.ExtraPaymentCertificateRecordMapper">

  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    id, form_num formNum, payment_no paymentNo, retry_times retryTimes, remark, create_time createTime, update_time updateTime
  </sql>

  <update id="updateRetryTimeById" parameterType="com.xhs.oa.bankcorporate.model.TPaymentCertificateRecord">
    update t_payment_certificate_record set payment_no = #{paymentNo}, retry_times = #{retryTimes},remark = #{remark} where id = #{id}
  </update>

  <select id="selectByFormPaymentNo" resultType="com.xhs.oa.bankcorporate.model.TPaymentCertificateRecord">
    select <include refid="Base_Column_List"></include>
    from t_payment_certificate_record
    where form_num = #{formNum} order by id desc limit 1
  </select>

</mapper>