<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhs.oa.bankcorporate.mapper.TPaymentCertificateRecordMapper">
  <resultMap id="BaseResultMap" type="com.xhs.oa.bankcorporate.model.TPaymentCertificateRecord">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="form_num" jdbcType="VARCHAR" property="formNum" />
    <result column="payment_no" jdbcType="VARCHAR" property="paymentNo" />
    <result column="retry_times" jdbcType="INTEGER" property="retryTimes" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    id, form_num, payment_no, retry_times, remark, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.xhs.oa.bankcorporate.model.TPaymentCertificateRecord" resultMap="BaseResultMap">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_payment_certificate_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    select 
    <include refid="Base_Column_List" />
    from t_payment_certificate_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    delete from t_payment_certificate_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.xhs.oa.bankcorporate.model.TPaymentCertificateRecordExample">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    delete from t_payment_certificate_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.xhs.oa.bankcorporate.model.TPaymentCertificateRecord">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    insert into t_payment_certificate_record (id, form_num, payment_no, 
      retry_times, remark, create_time, 
      update_time)
    values (#{id,jdbcType=BIGINT}, #{formNum,jdbcType=VARCHAR}, #{paymentNo,jdbcType=VARCHAR}, 
      #{retryTimes,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.xhs.oa.bankcorporate.model.TPaymentCertificateRecord" useGeneratedKeys="true" keyProperty="id">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    insert into t_payment_certificate_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="formNum != null">
        form_num,
      </if>
      <if test="paymentNo != null">
        payment_no,
      </if>
      <if test="retryTimes != null">
        retry_times,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="formNum != null">
        #{formNum,jdbcType=VARCHAR},
      </if>
      <if test="paymentNo != null">
        #{paymentNo,jdbcType=VARCHAR},
      </if>
      <if test="retryTimes != null">
        #{retryTimes,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.xhs.oa.bankcorporate.model.TPaymentCertificateRecordExample" resultType="java.lang.Long">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    select count(*) from t_payment_certificate_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>

  <update id="updateByExampleSelective" parameterType="map">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    update t_payment_certificate_record
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.formNum != null">
        form_num = #{row.formNum,jdbcType=VARCHAR},
      </if>
      <if test="row.paymentNo != null">
        payment_no = #{row.paymentNo,jdbcType=VARCHAR},
      </if>
      <if test="row.retryTimes != null">
        retry_times = #{row.retryTimes,jdbcType=INTEGER},
      </if>
      <if test="row.remark != null">
        remark = #{row.remark,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    update t_payment_certificate_record
    set id = #{row.id,jdbcType=BIGINT},
      form_num = #{row.formNum,jdbcType=VARCHAR},
      payment_no = #{row.paymentNo,jdbcType=VARCHAR},
      retry_times = #{row.retryTimes,jdbcType=INTEGER},
      remark = #{row.remark,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.xhs.oa.bankcorporate.model.TPaymentCertificateRecord">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    update t_payment_certificate_record
    <set>
      <if test="formNum != null">
        form_num = #{formNum,jdbcType=VARCHAR},
      </if>
      <if test="paymentNo != null">
        payment_no = #{paymentNo,jdbcType=VARCHAR},
      </if>
      <if test="retryTimes != null">
        retry_times = #{retryTimes,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.bankcorporate.model.TPaymentCertificateRecord">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    update t_payment_certificate_record
    set form_num = #{formNum,jdbcType=VARCHAR},
      payment_no = #{paymentNo,jdbcType=VARCHAR},
      retry_times = #{retryTimes,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>