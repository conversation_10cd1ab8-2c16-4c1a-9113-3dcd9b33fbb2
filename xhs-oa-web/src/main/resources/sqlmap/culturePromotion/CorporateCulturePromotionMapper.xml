<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.culturePromotion.mapper.CorporateCulturePromotionMapper" > 

	<sql id="BaseColumn">
          id     id,
          user_id     userId,
          greeting     greeting,
          audit_status     auditStatus,
          is_valid     isValid,
          creator_no     creator<PERSON><PERSON>,
          creator     creator,
          create_time     createTime,
          updator_no     updatorNo,
          updator     updator,
          update_time     updateTime
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.culturePromotion.model.CorporateCulturePromotion">
	    select 
	    <include refid="BaseColumn"/>
	    from corporate_culture_promotion
	    where id = #{id}
	</select>

    <select id="queryGreetingsByPage" resultType="com.xhs.oa.culturePromotion.model.CorporateCulturePromotion">
        select <include refid="BaseColumn"/>
        from corporate_culture_promotion
        where is_valid=1
        order by audit_status desc, id desc
        limit #{start}, #{pageSize}
    </select>

    <select id="queryGreetingsCount" resultType="java.lang.Integer">
        select count(1)
        from corporate_culture_promotion
        where is_valid=1
    </select>

    <select id="findGreetingCountByAuditType" resultType="java.lang.Integer">
        select count(1)
        from corporate_culture_promotion
        where is_valid=1 and audit_status=#{auditStatus}
    </select>


    <insert id="insert" parameterType="com.xhs.oa.culturePromotion.model.CorporateCulturePromotion">
    insert into corporate_culture_promotion (
          id,
          user_id,
          greeting,
          audit_status,
          is_valid,
          creator_no,
          creator,
          create_time,
          updator_no,
          updator,
          update_time
      )
    values (
           #{id},
           #{userId},
           #{greeting},
           #{auditStatus},
           #{isValid},
           #{creatorNo},
           #{creator},
           #{createTime},
           #{updatorNo},
           #{updator},
          #{updateTime}
      )
  </insert>

  
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.culturePromotion.model.CorporateCulturePromotion">
    update corporate_culture_promotion
    set 
           id = #{id},

           user_id = #{userId},

           greeting = #{greeting},

           audit_status = #{auditStatus},

           is_valid = #{isValid},

           creator_no = #{creatorNo},

           creator = #{creator},

           create_time = #{createTime},

           updator_no = #{updatorNo},

           updator = #{updator},

         update_time = #{updateTime}
    where id = #{id}
  </update>

    <update id="updateAuditStatusByPrimaryKey">
        update corporate_culture_promotion
        set audit_status=#{targetAuditStatus}, updator_no=#{userId}, updator=#{userName}, update_time=now()
        where is_valid=1 and id=#{id} and audit_status=#{auditStatus}
    </update>

    <update id="batchUpdateAuditStatus">
        update corporate_culture_promotion
        set audit_status=#{targetAuditStatus}, updator_no=#{userId}, updator=#{userName}, update_time=now()
        where is_valid=1 and audit_status=#{auditStatus} and id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>

</mapper>   
