<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.itemCollection.mapper.TItemsMapper" > 

	<sql id="BaseColumn">
          id id,
          item     item,
          department_id  departmentId,
          is_valid     isValid,
          operator_no     operatorNo,
          operator     operator,
          operate_time     operateTime,
          pop_up_message_status popUpMessageStatus,
          headline headline,
          pop_up_content popUpContent,
          item_employee_type itemEmployeeType
    </sql>

    <delete id="deleteItem">
        delete from t_items
        where id=#{id}
    </delete>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.itemCollection.model.TItems">
	    select 
	    <include refid="BaseColumn"/>
	    from t_items
	    where id = #{id} and is_valid=1
	</select>

    <select id="selectFirst" resultType="com.xhs.oa.itemCollection.model.TItems">
        select <include refid="BaseColumn"></include>
        from t_items
        where is_valid=1
        order by id
        limit 1
    </select>

    <select id="queryItemsByDeptIdAndItemName" resultType="com.xhs.oa.itemCollection.model.TItems">
        select <include refid="BaseColumn"></include>
        from t_items
        where is_valid=1 and department_id=#{departmentId} and item=#{itemName}
    </select>

    <select id="selectByDepartmentId" resultType="com.xhs.oa.itemCollection.model.TItems">
        select <include refid="BaseColumn"></include>
        from t_items
        where is_valid=1 and department_id=#{departmentId}
    </select>

    <select id="queryItemsByIds" resultType="com.xhs.oa.itemCollection.model.TItems">
        select <include refid="BaseColumn"></include>
        from t_items
        where id in
        <foreach collection="list" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <select id="queryItemSpecNameByItemIds" resultType="com.xhs.oa.itemCollection.dto.ItemSpecDTO">
        select
            a.id itemId,
            a.item itemName,
            b.id specId,
            b.specification specName,
            b.unit unit
        from t_items a
        join t_items_spec b on b.item_id=a.id and b.is_valid=1
        where a.id in
        <foreach collection="list" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>


    <insert id="insert" parameterType="com.xhs.oa.itemCollection.model.TItems" keyProperty="id" useGeneratedKeys="true">
        insert into t_items (item,
                             department_id,
                             is_valid,
                             operator_no,
                             operator,
                             operate_time,
                             pop_up_message_status,
                             headline,
                             pop_up_content,
                             item_employee_type)
        values (#{item},
                #{departmentId},
                1,
                #{operatorNo},
                #{operator},
                now(),
                #{popUpMessageStatus},
                #{headline},
                #{popUpContent},
                #{itemEmployeeType})
  </insert>

  
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.itemCollection.model.TItems">
    update t_items
    set 
           id = #{id},

           item = #{item},

           is_valid = #{isValid},

           operator_no = #{operatorNo},

           operator = #{operator},

         operate_time = #{operateTime}
    where id = #{id}
  </update>

</mapper>   
