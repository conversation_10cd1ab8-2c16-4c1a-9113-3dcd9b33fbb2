<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.itemCollection.mapper.TItemsAreaMapper" > 

	<sql id="BaseColumn">
          id     id,
          area     area,
          item_id   itemId,
          is_valid     isValid,
          operator_no     operatorNo,
          operator     operator,
          operate_time     operateTime
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.itemCollection.model.TItemsArea">
	    select 
	    <include refid="BaseColumn"/>
	    from t_items_area
	    where id = #{id} and is_valid=1
	</select>

    <select id="queryAreasByDepartmentId" resultType="com.xhs.oa.itemCollection.model.TItemsArea">
        select
          a.id     id,
          a.area     area,
          a.item_id   itemId,
          a.is_valid     isValid,
          a.operator_no     operatorNo,
          a.operator     operator,
          a.operate_time     operateTime
        from t_items_area a
        join t_items b on a.item_id=b.id and b.is_valid=1
        where b.department_id=#{departmentId} and a.is_valid=1
        order by a.id
    </select>

    <select id="queryItemAreaExist" resultType="java.lang.Integer">
        select count(1)
        from t_items_area a
        join t_items b on a.item_id=b.id and b.is_valid=1
        where a.is_valid=1 and b.department_id=#{departmentId} and b.item=#{itemName} and a.area=#{areaName}
    </select>

    <select id="queryAreasByItemIds" resultType="com.xhs.oa.itemCollection.model.TItemsArea">
        select <include refid="BaseColumn"></include>
        from t_items_area
        where is_valid=1 and item_id in
        <foreach collection="list" item="itemId" separator="," open="(" close=")">
            #{itemId}
        </foreach>
    </select>

    <select id="queryAreaByName" resultType="com.xhs.oa.itemCollection.model.TItemsArea">
        select <include refid="BaseColumn"></include>
        from t_items_area
        where is_valid=1 and area=#{area}
    </select>


    <insert id="insert" parameterType="com.xhs.oa.itemCollection.model.TItemsArea" useGeneratedKeys="true" keyProperty="id">
    insert into t_items_area (
          area,
          item_id,
          is_valid,
          operator_no,
          operator,
          operate_time
      )
    values (
           #{area},
           #{itemId},
           1,
           #{operatorNo},
           #{operator},
           now()
      )
  </insert>

  
  <update id="updateByPrimaryKey" parameterType="com.xhs.oa.itemCollection.model.TItemsArea">
    update t_items_area
    set 
           id = #{id},

           area = #{area},

           item_id=#{itemId},

           is_valid = #{isValid},

           operator_no = #{operatorNo},

           operator = #{operator},

         operate_time = #{operateTime}
    where id = #{id}
  </update>

    <update id="deleteArea">
        delete from t_items_area
        where id=#{id}
    </update>

</mapper>   
