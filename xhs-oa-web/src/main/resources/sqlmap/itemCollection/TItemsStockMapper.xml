<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xhs.oa.itemCollection.mapper.TItemsStockMapper" > 

	<sql id="BaseColumn">
          id     id,
          item_area_id     itemAreaId,
          item_spec_id     itemSpecId,
          total_stock     totalStock,
          stock     stock,
          operator_no     operatorNo,
          operator     operator,
          operate_time     operateTime,
          operator_phone     operatorPhone
    </sql>

	<select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="com.xhs.oa.itemCollection.model.TItemsStock">
	    select 
	    <include refid="BaseColumn"/>
	    from t_items_stock
	    where id = #{id}
	</select>

    <select id="selectByAreaAndSpec" resultType="com.xhs.oa.itemCollection.model.TItemsStock">
        select <include refid="BaseColumn"></include>
        from t_items_stock
        where item_area_id=#{areaId} and item_spec_id=#{specId}
    </select>

    <resultMap id="areaStock" type="com.xhs.oa.itemCollection.dto.AreaStockDTO">
        <id column="areaId" property="areaId"/>
        <result column="area" property="areaName"/>
        <result column="item" property="itemName"/>

        <collection property="specStockDTOs" ofType="com.xhs.oa.itemCollection.dto.SpecStockDTO">
            <id column="specId" property="specId"/>
            <result column="totalStock" property="totalStock"/>
            <result column="stock" property="leftStock"/>
        </collection>
    </resultMap>

    <select id="queryAreaStock" resultMap="areaStock">
        select
            a.area area,
            c.item item,
            b.item_area_id areaId,
            b.item_spec_id specId,
            b.total_stock totalStock,
            b.stock stock
        from t_items_area a
        join t_items c on a.item_id=c.id and c.is_valid=1
        left join t_items_stock b on b.item_area_id=a.id
        where a.is_valid=1 and a.id in
        <foreach collection="areaIds" item="areaId" separator="," open="(" close=")">
            #{areaId}
        </foreach>
        order by a.id
    </select>


    <insert id="insert" parameterType="com.xhs.oa.itemCollection.model.TItemsStock">
    insert into t_items_stock (
          id,
          item_area_id,
          item_spec_id,
          total_stock,
          stock,
          operator_no,
          operator,
          operate_time
      )
    values (
           #{id},
           #{itemAreaId},
           #{itemSpecId},
           #{totalStock},
           #{stock},
           #{operatorNo},
           #{operator},
          #{operateTime}
      )
  </insert>

    <insert id="batchInsert">
    insert into t_items_stock (
          item_area_id,
          item_spec_id,
          total_stock,
          stock,
          operator_no,
          operator,
          operate_time
      )
    values
    <foreach collection="list" item="item" separator=",">
     (
           #{item.itemAreaId},
           #{item.itemSpecId},
           #{item.totalStock},
           #{item.stock},
           #{item.operatorNo},
           #{item.operator},
           now()
      )
    </foreach>
    </insert>


    <update id="updateStockByPrimaryKey" parameterType="com.xhs.oa.itemCollection.model.TItemsStock">
    update t_items_stock
    set
           total_stock = #{paramsMap.newTotalStock},

           stock = #{paramsMap.newStock},

           operator_no = #{paramsMap.operatorNo},

           operator = #{paramsMap.operator},

         operate_time = now(),

         operator_phone=#{paramsMap.operatorPhone}
    where id = #{paramsMap.stockId}
        and total_stock = #{paramsMap.totalStock}
        and stock = #{paramsMap.stock}
  </update>

    <update id="decreaseStock">
        update t_items_stock
        set stock=stock-#{paramsMap.amount}
        <if test="paramsMap.operatorNo!=null">
            , operator_no=#{paramsMap.operatorNo}
        </if>
        <if test="paramsMap.operator!=null">
            , operator=#{paramsMap.operator}
        </if>
        <if test="paramsMap.operatorPhone!=null">
            , operator_phone=#{paramsMap.operatorPhone}
        </if>
        , operate_time=now()
        where stock=#{paramsMap.stock} and item_area_id=#{paramsMap.areaId} and item_spec_id=#{paramsMap.specId}
    </update>


</mapper>   
