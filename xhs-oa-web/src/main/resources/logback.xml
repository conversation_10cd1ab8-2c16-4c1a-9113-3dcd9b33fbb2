<?xml version="1.0" encoding="UTF-8" ?>
<configuration>

    <property name="context_name" value="xhsoa"/>

    <conversionRule conversionWord="tracelogid" converterClass="com.xhs.finance.log.TraceLogIdConverter"/>

    <appender name="file_log" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>xhsoa.log</file>
        <Prudent>true</Prudent>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>xhsoa.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>90</maxHistory>
        </rollingPolicy>
        <encoder>
            <!-- <pattern>%d{HH:mm:ss.SSS} [%tracelogid] %-5level [%lineno] - %msg%n</pattern> -->
            <pattern> %d{HH:mm:ss.SSS} [%-5level] [%tracelogid] %file:[%line] - %msg%n</pattern>
            <!-- <immediateFlush>true</immediateFlush> -->
            <charset class="java.nio.charset.Charset">UTF-8</charset>
        </encoder>
    </appender>

    <appender name="console_log" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%-5level] [%tracelogid] %file:[%line] - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="json_log" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="com.xiaohongshu.infra.utils.utils.logappender.layout.MDCJsonPatternLayout">
                <pattern>{"app": "xhsoa", "prdline": "erp", "timestamp":"%date{yyyy-MM-dd HH:mm:ss}","tracelogid":"%tracelogid", "traceId":"%X{traceId}","spanId":"%X{spanId}","catId":"%X{catMessageId}","xsMsgId":"%X{xsMsgId}","logger":"%logger","file":"%file","line":"%line", "log_level": "%level","message": %message, "stack_trace": %exception%nopex}%n</pattern>
            </layout>
        </encoder>
    </appender>

    <appender name="catAppender" class="com.xiaohongshu.infra.utils.utils.logappender.CatLogbackAppender"></appender>
    <appender name ="rocketmqAppender" class="com.xhs.enterprise.alarm.api.RocketMqLogbackAppender">
        <appId>xhsoa</appId>
        <tags>test</tags>
    </appender>
    <root>
        <level value="error"/>
        <appender-ref ref="catAppender"/>
        <appender-ref ref="rocketmqAppender"/>
    </root>



    <logger name="com.xhs.oa" level="info"></logger>
    <logger name="org.apache.ibatis" level="INFO"></logger>
    <logger name="org.apache.zookeeper" level="info"></logger>
    <logger name="org.springframework" level="INFO"></logger>
    <logger name="springfox" level="error"></logger>
    <logger name="com.xiaohongshu.infra.utils.mybatis.MybatisSQLInterceptor" level="ERROR"></logger>
    <logger name="com.xiaohongshu.infra.rpc.core" level="ERROR"></logger>
    <logger name="RocketmqClient" level="ERROR"></logger>
    <logger name="RocketmqRemoting" level="ERROR"></logger>

    <root level="info">
        <appender-ref ref="json_log"/>
        <!--<appender-ref ref="console_log"/>-->
        <appender-ref ref="file_log"/>
    </root>


</configuration>
