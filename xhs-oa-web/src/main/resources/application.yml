cat:
    name: xhsoa
apm:
    monitor:
       name: cat

management:
  endpoints:
    jmx:
      exposure:
        exclude: "*"
    web:
      exposure:
        include: health,info,prometheus,readiness
#一票通行配置 参数配置参考：https://docs.xiaohongshu.com/doc/49d0fd7839c132d4aa47158553ae31a2?anchorLinkId=block-cd89c4291e4b65a5a58145c58c3dfc33
sso:
  accessor:
    final:
      publicKey: MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE0H7gGUM8br6pzpsi5dEElVo6QIkJd0G5lzLA1fcBxMOStCzw+T3LN49yqvO86NEeACUf+9bZiDMKFmI0nwxkwQ==
      openAuth: false
      openPossessionLogin: true
      enableCatLog: true
      enableOasisHeader: false
      loginWhiteList:
        - /xhs-oa/health/**
        - /xhs-oa/index**
        - /xhs-oa/assets/**
        - /xhs-oa/swagger**
        - /xhs-oa/swagger-resources/**
        - /xhs-oa/webjars/**
        - /xhs-oa/v2/**
        - /xhs-oa/images/**
        - /xhs-oa/css/**
        - /xhs-oa/fonts/**
        - /xhs-oa/img/**
        - /xhs-oa/favicon.ico
        - /xhs-oa/oa/loginController/login
        - /xhs-oa/oa/loginController/ssoLogin
        - /xhs-oa/oa/loginController/ssoLoginV2
        - /xhs-oa/oa/loginController/sendForgetPwdEmail
        - /xhs-oa/oa/loginController/saveNewPwdWithForget
        - /xhs-oa/oa/loginController/loginFormSSO
        - /xhs-oa/modeler**
        - /xhs-oa/editor-app/**
        - /xhs-oa/oa/oaWechatController/getUserInfoByCodeForH5
        - /xhs-oa/oa/processgraph/editor/**
        - /xhs-oa/oa/okrController/getUserInfoByCodeForH5
        - /xhs-oa/items/getUserInfoByCodeForH5
        - /xhs-oa/oa/accessSysSyncController/**
        - /xhs-oa/performance/performanceLoginController/getUserInfoByCodeForH5
        - /xhs-oa/oa/redName/getUserInfoByCodeForH5
        - /xhs-oa/oa/redName/checkRedNameList
        - /xhs-oa/oa/redNameAI/receiveChatbotMessage
        - /xhs-oa/oa/redNameAI/getAIWelcomeMessages
        - /xhs-oa/api/outterApiController/queryEmployeeInfoByPage
        - /xhs-oa/api/outterApiController/queryEmployeeInfoByPageToSit
        - /xhs-oa/api/outterApiController/queryDepartmentInfoByPageToSit
        - /xhs-oa/api/outterApiController/queryEmployee
        - /xhs-oa/purchasebid/purchaseSourceController/getQuoteDesc
        - /xhs-oa/purchasebid/purchaseSourceController/fillQuote
        - /xhs-oa/api/outterApiController/queryOuterAccountInfo
        - /xhs-oa/api/outterApiController/queryOuterAccountInfoByXhsId
        - /xhs-oa/api/outterApiController/batchQueryOuterAccountInfoByXhsId
        - /xhs-oa/api/outterApiController/querySyncEmployeeInfoByPage
        - /xhs-oa/api/outterApiController/travel/queryTravelScheduleDateList
        - /xhs-oa/api/auditMiddlePlatformController/workflowDeploy
        - /xhs-oa/api/auditMiddlePlatformController/workflowPreview
        - /xhs-oa/variableCondition/variableConditionController/**
        - /xhs-oa/elVariable/elVariableController/**
        - /xhs-oa/oauth2/**
        - /xhs-oa/payment/BidOrderPaymentController/submitUploadInvoices
        - /xhs-oa/oa/workflowController/intellectualAuditPass
        - /xhs-oa/oa/commonForm/getCurrencyByName
        - /xhs-oa/oa/workflowController/waitDoTaskAuditPassOrRefuse
        - /xhs-oa/oa/workflowController/queryFormData
        - /xhs-oa/items/out/**
        - /xhs-oa/api/travel/provider/**
        - /xhs-oa/didi/didiDockingController/**
        - /xhs-oa/redname/outer/**
