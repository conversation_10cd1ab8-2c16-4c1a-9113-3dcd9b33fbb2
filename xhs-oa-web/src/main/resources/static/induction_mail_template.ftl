<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>新员工入职信息开通</title>
</head>

<body>
<style type="text/css"> .header {
    border-bottom: 1px solid #AEAEAE;
    padding-bottom: 30px;
}
.logo {
    color: red;
    font-weight: bold;
    font-size: 24px
}
.main {
    padding-top: 10px;
    padding-bottom: 10px;
    border-bottom: 1px dashed gray;
}
.footer {
    margin-top: 50px;
    text-align: center;
    width: 100%
}
.end {
    padding: 20px 0 20px 0;
    border-bottom: 1px solid lightgray;
}
</style>

<div class="main">
    <p>Dear  ${acceptor}：</p>
    <p>
        ${variedWords}：${email}，邮箱初始密码：${emailPwd}
        <br>域账号：${samAccountName}  域密码：与邮箱密码相同
        <br>邮箱和域账号会在您收到本邮件后约1小时生效。
    </p>
    <p>
        公司统一IM工具“企业微信”，企微账号将在HR系统确认入职后开通，您可以前往 【企业微信 - 工作台 - HR助手】查询各类新人指南。
        <br>公司内无线WiFi、文件共享盘、打印机、SSL VPN均使用域账号登陆，详细使用方法请参考 【企业微信 - 工作台 - IT助手】。
    </p>
    <p>
        <b>请在账号激活后，尽快修改初始密码<b>。修改方法：进入【企业微信-工作台-IT助手】，在底部菜单找到【账号自助-域账号密码重置】，按照页面提示操作即可。
    </p>
    <p>
        ---
    </p>
    <p>Dear  ${acceptor}：</p>
    <p>
        ${variedWordsEn}：${email}, Initial email password: ${emailPwd}
        <br>Domain account:${samAccountName}  Domain password: Synchronized with email password
        <br>The account will take effect approximately 1 hour after receiving the email.
    </p>
    <p>
        Our company uses "WeCom" as an internal instant messaging tool, the account will be activated after the eHR system confirms your onboarding.<br>
        <br>Also, you can use domain account to log in to WiFi, shared disk files, printers and SSL VPNs. Please refer to WeCom - IT Assistant for more details.<br>
    </p>
    <p>
        <b>After account activation, please change the initial password ASAP. </b>
        <br>Access WeCom's "Workspace", find "<img style="position:relative;width:32px;top:7px" src="https://picasso-static.xiaohongshu.com/fe-platform/f3c31a88ba2d1ee10e6d40244154168ef4a9fd33.png">IT Assistant", select "Account Self-Service", and click on "Domain Account Password Reset" to modify the password.<br>
    </p>
</div>

<div class="end">
    <p>
        本邮件由系统发出，请勿直接回复。
        <br>This email is sent by the system, please do not reply directly.
    </p>
</div>
<div class="footer">
    <#--<div class="logo">
        <img src="http://s.xiaohongshu.com/formula-static/frieza/public/img/logo.abd6929.png" title="小红书_logo" style="width: 99px; height: 50px;">
    </div>-->
    <a href="https://www.xiaohongshu.com" target="_blank">www.xiaohongshu.com</a>
</div>
</body>

</html>