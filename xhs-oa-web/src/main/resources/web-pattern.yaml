ignoreUrls:
  - /health/**
  - /index**
  - /assets/**
  - /swagger**
  - /swagger-resources/**
  - /webjars/**
  - /v2/**
  - /images/**
  - /css/**
  - /fonts/**
  - /img/**
  - /favicon.ico
  - /oa/loginController/login
  - /oa/loginController/ssoLogin
  - /oa/loginController/ssoLoginV2
  - /oa/loginController/sendForgetPwdEmail
  - /oa/loginController/saveNewPwdWithForget
  - /oa/loginController/loginFormSSO
  - /modeler**
  - /editor-app/**
  - /oa/oaWechatController/getUserInfoByCodeForH5
  - /oa/processgraph/editor/**
  - /oa/okrController/getUserInfoByCodeForH5
  - /items/getUserInfoByCodeForH5
  - /oa/accessSysSyncController/**
  - /performance/performanceLoginController/getUserInfoByCodeForH5
  - /oa/redName/getUserInfoByCodeForH5
  - /oa/redName/checkRedNameList
  - /oa/redNameAI/receiveChatbotMessage
  - /oa/redNameAI/getAIWelcomeMessages
  - /api/outterApiController/queryEmployeeInfoByPage
  - /api/outterApiController/queryEmployeeInfoByPageToSit
  - /api/outterApiController/queryDepartmentInfoByPageToSit
  - /api/outterApiController/queryEmployee
  - /purchasebid/purchaseSourceController/getQuoteDesc
  - /purchasebid/purchaseSourceController/fillQuote
  - /api/outterApiController/queryOuterAccountInfo
  - /api/outterApiController/queryOuterAccountInfoByXhsId
  - /api/outterApiController/batchQueryOuterAccountInfoByXhsId
  - /api/outterApiController/querySyncEmployeeInfoByPage
  - /api/outterApiController/travel/queryTravelScheduleDateList
  - /api/auditMiddlePlatformController/workflowDeploy
  - /api/auditMiddlePlatformController/workflowPreview
  - /variableCondition/variableConditionController/**
  - /elVariable/elVariableController/**
  - /oauth2/**
  - /payment/BidOrderPaymentController/submitUploadInvoices
  - /oa/workflowController/intellectualAuditPass
  - /oa/commonForm/getCurrencyByName
  - /oa/workflowController/waitDoTaskAuditPassOrRefuse
  - /oa/workflowController/queryFormData
  - /items/out/**
  - /api/travel/provider/**
  - /didi/didiDockingController/**